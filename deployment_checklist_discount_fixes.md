# 🚀 Deployment Checklist - Discount Code Fixes

## 📋 **Files to Upload to Server:**

### 1. **Modified Files:**
- [ ] `mip_system.py` - Backend discount logic fixes
- [ ] `templates/marketplace/storefront.html` - Frontend cart calculation fixes  
- [ ] `templates/marketplace/order_detail.html` - UI column reorder (<PERSON><PERSON><PERSON> cập first)

### 2. **Migration Script:**
- [ ] `migrate_add_total_savings_server.py` - Database migration for total_savings column

## 🔧 **Deployment Steps:**

### Step 1: Upload Files
```bash
# Upload modified files to server
scp mip_system.py user@server:/path/to/app/
scp templates/marketplace/storefront.html user@server:/path/to/app/templates/marketplace/
scp templates/marketplace/order_detail.html user@server:/path/to/app/templates/marketplace/
scp migrate_add_total_savings_server.py user@server:/path/to/app/
```

### Step 2: Run Migration
```bash
# SSH to server
ssh user@server

# Navigate to app directory
cd /path/to/app/

# Run migration
python3 migrate_add_total_savings_server.py
```

**Expected Output:**
```
🚀 Server Migration: Add total_savings column to Discounts table
============================================================
✅ Successfully imported db_config
📊 Connected to database: sapmmo as user: alandoan
✅ Discounts table found
🔧 Adding total_savings column to Discounts table...
✅ Successfully added total_savings column
✅ Initialized X existing discount records
✅ total_savings column verified successfully
🎉 Migration completed successfully!
```

### Step 3: Restart Services
```bash
# Restart application service (adjust command as needed)
sudo systemctl restart sapmmo
# OR
sudo service sapmmo restart
# OR if using gunicorn directly
pkill -f gunicorn && gunicorn --bind 0.0.0.0:8000 mip_system:app
```

### Step 4: Verify Deployment
- [ ] Check application starts without errors
- [ ] Test marketplace storefront loads
- [ ] Test discount code functionality

## 🧪 **Testing Checklist:**

### 1. **Frontend Cart Calculation:**
- [ ] Add product to cart (e.g., 100,000 MP)
- [ ] Apply test discount code `TEST100` (100% off)
- [ ] Verify display:
  - [ ] Subtotal: 100,000 MP
  - [ ] Discount: -100,000 MP  
  - [ ] **Total: 0 MP** ✅
- [ ] Check with low balance (e.g., 50,000 MP):
  - [ ] Should allow checkout ✅
  - [ ] Button should show "Thanh toán" not "Số dư không đủ" ✅

### 2. **Backend Transaction Recording:**
- [ ] Complete checkout with discount
- [ ] Check transaction history:
  - [ ] MP deducted: 0 MP ✅
  - [ ] Description includes: "(Áp dụng mã TEST100, tiết kiệm 100,000 MP)" ✅
- [ ] Check admin discount stats:
  - [ ] `used_count` increased by 1 ✅
  - [ ] `total_savings` increased by discount amount ✅

### 3. **UI Improvements:**
- [ ] Go to `/marketplace/orders/{order_id}` for video order
- [ ] Check video links table:
  - [ ] "Truy cập" column is first ✅
  - [ ] Easy to access on mobile ✅
  - [ ] No horizontal scrolling needed ✅

## 🔍 **Troubleshooting:**

### Migration Issues:
```bash
# If migration fails, check:
1. db_config.py exists and has correct PG_CONFIG
2. Database connection works
3. User has ALTER TABLE permissions
```

### Application Issues:
```bash
# Check logs
tail -f /var/log/sapmmo/error.log
# OR
journalctl -u sapmmo -f
```

### Database Issues:
```bash
# Connect to database manually
psql -h localhost -U alandoan -d sapmmo

# Check if column exists
\d "Discounts"

# Check sample data
SELECT code, used_count, total_savings FROM "Discounts" LIMIT 5;
```

## 📊 **Verification Queries:**

```sql
-- Check total_savings column exists
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'Discounts' AND column_name = 'total_savings';

-- Check discount codes
SELECT code, used_count, total_savings, is_active 
FROM "Discounts" 
ORDER BY created_at DESC;

-- Check recent transactions with discounts
SELECT description, amount, created_at 
FROM "MPTransactions" 
WHERE description LIKE '%Áp dụng mã%' 
ORDER BY created_at DESC 
LIMIT 10;
```

## ✅ **Success Criteria:**

- [ ] Migration runs without errors
- [ ] Application starts successfully  
- [ ] Discount calculation shows correct totals
- [ ] Checkout works with 0 MP final amount
- [ ] Transaction history shows correct amounts and descriptions
- [ ] Video order details show "Truy cập" column first
- [ ] Mobile UI is improved (no horizontal scroll needed)

## 🎯 **Rollback Plan (if needed):**

```sql
-- Remove total_savings column if needed
ALTER TABLE "Discounts" DROP COLUMN IF EXISTS total_savings;
```

```bash
# Restore original files
git checkout HEAD -- mip_system.py templates/marketplace/storefront.html templates/marketplace/order_detail.html
```
