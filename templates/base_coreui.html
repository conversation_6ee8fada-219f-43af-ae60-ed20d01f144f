<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, shrink-to-fit=no">
    <title>SAPMMO System - {% block title %}{% endblock %}</title>
    <link rel="icon" type="image/png" href="{{ url_for('static', filename='images/logo.png') }}"

    <!-- CoreUI CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@coreui/coreui@4.2.6/dist/css/coreui.min.css">
    <!-- CoreUI Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@coreui/icons@2.1.0/css/all.min.css">

    <!-- jQuery (required for some plugins) -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Select2 -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />

    <!-- DataTables -->
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='custom.css') }}">
    <style>
        .select2-container--bootstrap-5 .select2-selection {
            min-height: 38px;
        }

        .c-sidebar-nav-link {
            display: flex;
            align-items: center;
        }

        .c-sidebar-nav-icon {
            margin-right: 0.5rem;
        }

        .c-app {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        .c-wrapper {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .c-body {
            flex: 1;
            padding: 1.5rem;
        }

        .c-footer {
            padding: 1rem;
            background-color: #f8f9fa;
            border-top: 1px solid #d8dbe0;
        }

        /* Điều chỉnh sidebar */
        .sidebar {
            width: 260px;
            transition: all 0.3s ease;
            position: fixed;
            top: 0;
            bottom: 0;
            left: 0;
            z-index: 1030;
        }

        .sidebar-brand-narrow {
            display: none;
        }

        /* Các CSS cho sidebar đã được xử lý bởi CoreUI */

        /* Điều chỉnh wrapper */
        .wrapper {
            margin-left: 260px;
            transition: all 0.3s ease;
            width: calc(100% - 260px);
        }

        .sidebar.sidebar-narrow-unfoldable ~ .wrapper {
            margin-left: 4rem !important;
            width: calc(100% - 4rem) !important;
        }

        /* Điều chỉnh sidebar toggler */
        .sidebar-toggler {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 3rem;
            background-color: rgba(0, 0, 0, 0.2);
            border: none;
            color: rgba(255, 255, 255, 0.6);
            cursor: pointer;
            z-index: 1040;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0;
            margin: 0;
            text-align: center;
        }

        .sidebar-toggler:hover {
            color: #fff;
            background-color: rgba(0, 0, 0, 0.3);
        }

        .sidebar-toggler:focus {
            outline: none;
            box-shadow: none;
        }

        .sidebar-toggler:active {
            background-color: rgba(0, 0, 0, 0.4);
        }

        .sidebar-toggler i {
            font-size: 1.25rem;
            pointer-events: none; /* Đảm bảo sự kiện click không bị chặn bởi icon */
        }

        /* Điều chỉnh header toggler */
        .header-toggler {
            cursor: pointer;
            background: transparent;
            border: none;
            padding: 0.5rem;
            color: #3c4b64;
            font-size: 1.25rem;
        }

        .header-toggler:hover {
            color: #321fdb;
        }

        .header-toggler:focus {
            outline: none;
            box-shadow: none;
        }



        /* Logo styling */
        .sidebar-brand {
            background: white;
            border-bottom: 1px solid rgba(0, 198, 174, 0.1);
            box-shadow: 0 2px 8px rgba(0, 198, 174, 0.1);
        }

        .sidebar-brand img {
            transition: all 0.3s ease;
            filter: brightness(1.1) contrast(1.1);
        }

        .sidebar-brand img:hover {
            transform: scale(1.05);
            filter: brightness(1.2) contrast(1.2);
        }

        .sidebar-brand-full {
            display: flex;
            align-items: center;
            justify-content: flex-start;
        }

        .sidebar-brand-narrow {
            display: none;
            align-items: center;
            justify-content: center;
        }

        .sidebar.sidebar-narrow-unfoldable .sidebar-brand-full {
            display: none;
        }

        .sidebar.sidebar-narrow-unfoldable .sidebar-brand-narrow {
            display: flex;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .wrapper {
                width: 100%;
                margin-left: 0;
            }

            .sidebar.unfoldable ~ .wrapper {
                width: 100%;
                margin-left: 0;
            }
            .header-brand img {
                display: none !important;
            }
        }

        /* Điều chỉnh header và body */
        .header {
            position: sticky;
            top: 0;
            z-index: 1020;
            background-color: #fff;
            border-bottom: 1px solid #d8dbe0;
        }

        .body {
            padding-top: 1rem;
        }

        /* Điều chỉnh container */
        .container-lg {
            max-width: 100%;
            padding-left: 1rem;
            padding-right: 1rem;
        }

        /* Theme màu #00C6AE */
        .btn-primary {
            background-color: #00C6AE;
            border-color: #00C6AE;
        }

        .btn-primary:hover, .btn-primary:focus {
            background-color: #00A693;
            border-color: #00A693;
        }

        .nav-link.active {
            background-color: #00C6AE !important;
            color: white !important;
        }

        .nav-link.active span {
            color: white !important;
        }

        .nav-link.active i {
            color: white !important;
        }

        .nav-link:hover {
            background-color: rgba(0, 198, 174, 0.1);
        }

        .text-primary {
            color: #00C6AE !important;
        }

        .border-primary {
            border-color: #00C6AE !important;
        }

        .bg-primary {
            background-color: #00C6AE !important;
        }

        /* MP Balance styling */
        .mp-text {
            color: #00C6AE;
            font-size: 1.1rem;
        }

        /* Header brand logo for mobile */
        .header-brand img {
            transition: transform 0.3s ease;
        }

        .header-brand img:hover {
            transform: scale(1.05);
        }

        /* Mobile header improvements */
        @media (max-width: 768px) {
            /* Header layout */
            .header .container-fluid {
                padding: 0.5rem 1rem;
            }

            /* Top row layout: Menu | Logo | Icons */
            .header .d-flex:first-child {
                width: 100%;
            }

            /* Icons row - right aligned */
            .nav-link {
                padding: 0.5rem !important;
                display: flex;
                align-items: center;
                justify-content: center;
                min-width: 44px; /* Touch target size */
                min-height: 44px;
            }

            .nav-link i {
                font-size: 1.25rem;
            }

            /* MP Balance mobile - right aligned */
            .mp-balance-mobile {
                background: rgba(0, 198, 174, 0.1) !important;
                border-radius: 8px !important;
                padding: 8px 12px !important;
                display: inline-block !important;
                white-space: nowrap !important;
                margin-left: auto !important;
                margin-right: 0 !important;
            }

            /* Right align container */
            .d-md-none.mt-2.pb-2 {
                width: 100% !important;
                display: flex !important;
                justify-content: flex-end !important;
                align-items: center !important;
                text-align: right !important;
            }

            .mp-text {
                font-size: 0.9rem;
            }

            .btn-sm {
                padding: 0.25rem 0.5rem;
                font-size: 0.8rem;
            }

            /* Header toggler */
            .header-toggler {
                flex-shrink: 0;
                width: 44px;
                height: 44px;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            /* Logo container */
            .flex-grow-1 {
                flex-grow: 1;
            }

            /* Icons container */
            .d-flex.align-items-center:last-child {
                flex-shrink: 0;
            }
        }

        /* Desktop header */
        @media (min-width: 769px) {
            .mp-balance-mobile {
                display: none !important;
            }
        }

        /* Additional right alignment fixes */
        .header .container-fluid > div:last-child {
            text-align: right !important;
        }

        /* Icon fallback styles */
        .icon-fallback {
            display: inline-block;
            width: 24px;
            height: 24px;
            text-align: center;
            line-height: 24px;
            font-weight: bold;
            border-radius: 4px;
            background-color: #00C6AE;
            color: white;
            font-size: 14px;
        }
    </style>

    {% block head_extra %}{% endblock %}
</head>
<body>
    <div class="sidebar sidebar-fixed" id="sidebar">
        <div class="sidebar-brand d-none d-md-flex">
            <div class="sidebar-brand-full">
                <img src="{{ url_for('static', filename='images/logo.png') }}" alt="MIP System" style="height: 64px;">
            </div>
            <div class="sidebar-brand-narrow">
                <img src="{{ url_for('static', filename='images/logo.png') }}" alt="MIP" style="height: 45px; width: 45px; margin: 3px; object-fit: contain;">
            </div>
        </div>

        <ul class="sidebar-nav" data-coreui="navigation">
            <li class="nav-item">
                <a class="nav-link" href="/dashboard">
                    <i class="nav-icon cil-speedometer"></i> <span>Dashboard</span>
                </a>
            </li>

            <!-- Menu cho Leader -->
            {% if session.role == 'leader' %}
            <li class="nav-item">
                <a class="nav-link" href="/accounts">
                    <i class="nav-icon cil-list"></i> <span>Danh sách tài khoản</span>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link" href="/revenue">
                    <i class="nav-icon cil-chart-line"></i> <span>Doanh số</span>
                </a>
            </li>

            {% if session.role == 'admin' %}
            <li class="nav-item">
                <a class="nav-link" href="/commission_report">
                    <i class="nav-icon cil-file"></i> <span>Báo cáo hoa hồng</span>
                </a>
            </li>
            {% endif %}

            <li class="nav-item">
                <a class="nav-link" href="/transactions">
                    <i class="nav-icon cil-wallet"></i> <span>Quản lý giao dịch</span>
                </a>
            </li>

            <!-- Marketplace Menu cho Leader -->
            <li class="nav-group">
                <a class="nav-link nav-group-toggle" href="#">
                    <i class="nav-icon cil-basket"></i> <span>Marketplace</span>
                </a>
                <ul class="nav-group-items">
                    <li class="nav-item">
                        <a class="nav-link" href="/marketplace">
                            <i class="nav-icon cil-basket"></i> <span>Cửa hàng</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/marketplace/orders">
                            <i class="nav-icon cil-list"></i> <span>Đơn hàng của tôi</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/marketplace/aff-packages">
                            <i class="nav-icon cil-chart-line"></i> <span>AFF Packages</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/marketplace/warranty">
                            <i class="nav-icon cil-shield-alt"></i> <span>Bảo hành</span>
                        </a>
                    </li>
                </ul>
            </li>

            <!-- Menu cho Admin -->
            {% else %}
            <li class="nav-group">
                <a class="nav-link nav-group-toggle" href="#">
                    <i class="nav-icon cil-user"></i> <span>Quản lý tài khoản</span>
                </a>
                <ul class="nav-group-items">
                    <li class="nav-item">
                        <a class="nav-link" href="/accounts">
                            <i class="nav-icon cil-list"></i> <span>Danh sách tài khoản</span>
                        </a>
                    </li>

                </ul>
            </li>

            <li class="nav-item">
                <a class="nav-link" href="/teams">
                    <i class="nav-icon cil-people"></i> <span>Quản lý đội</span>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link" href="/users">
                    <i class="nav-icon cil-user"></i> <span>Quản lý người dùng</span>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link" href="/proxies">
                    <i class="nav-icon cil-lan"></i> <span>Quản lý Proxy</span>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link" href="/revenue">
                    <i class="nav-icon cil-chart-line"></i> <span>Doanh số</span>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link" href="/commissions">
                    <i class="nav-icon cil-dollar"></i> <span>Hoa hồng</span>
                </a>
            </li>

            {% if session.role == 'admin' %}
            <li class="nav-item">
                <a class="nav-link" href="/commission_report">
                    <i class="nav-icon cil-file"></i> <span>Báo cáo hoa hồng</span>
                </a>
            </li>
            {% endif %}

            <li class="nav-item">
                <a class="nav-link" href="/transactions">
                    <i class="nav-icon cil-wallet"></i> <span>Quản lý giao dịch</span>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link" href="/products">
                    <i class="nav-icon cil-star"></i> <span>Quản lý sản phẩm Win</span>
                </a>
            </li>

            <li class="nav-group">
                <a class="nav-link nav-group-toggle" href="#">
                    <i class="nav-icon cil-settings"></i> <span>Công cụ</span>
                </a>
                <ul class="nav-group-items">
                    <li class="nav-item">
                        <a class="nav-link" href="/tools">
                            <i class="nav-icon cil-wrench"></i> <span>Tất cả công cụ</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/gologin">
                            <i class="nav-icon cil-browser"></i> <span>GoLogin Management</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/get_verification_code">
                            <i class="nav-icon cil-envelope-closed"></i> <span>Lấy mã mail</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/mail_aliases">
                            <i class="nav-icon cil-envelope-letter"></i> <span>Quản lý Mail</span>
                        </a>
                    </li>
                </ul>
            </li>

            <!-- Marketplace Admin Menu -->
            <li class="nav-group">
                <a class="nav-link nav-group-toggle" href="#">
                    <i class="nav-icon cil-basket"></i> <span>Marketplace Admin</span>
                </a>
                <ul class="nav-group-items">
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/marketplace/categories">
                            <i class="nav-icon cil-grid"></i> <span>Quản lý danh mục</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/marketplace/product-types">
                            <i class="nav-icon cil-tag"></i> <span>Loại sản phẩm</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/marketplace/products">
                            <i class="nav-icon cil-tags"></i> <span>Quản lý sản phẩm</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/marketplace/video-links">
                            <i class="nav-icon cil-video"></i> <span>Quản lý Video Links</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/marketplace/aff-packages">
                            <i class="nav-icon cil-chart-line"></i> <span>AFF Packages</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/marketplace/orders">
                            <i class="nav-icon cil-cart"></i> <span>Quản lý đơn hàng</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/marketplace/discounts">
                            <i class="nav-icon cil-gift"></i> <span>Mã giảm giá</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/marketplace/warranty">
                            <i class="nav-icon cil-shield-alt"></i> <span>Yêu cầu bảo hành</span>
                        </a>
                    </li>
                </ul>
            </li>

            {% if session.role == 'admin' %}
            <li class="nav-group">
                <a class="nav-link nav-group-toggle" href="#">
                    <i class="nav-icon cil-settings"></i> <span>Cấu hình hệ thống</span>
                </a>
                <ul class="nav-group-items">
                    <li class="nav-item">
                        <a class="nav-link" href="/crontab">
                            <i class="nav-icon cil-clock"></i> <span>Quản lý Crontab</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/config">
                            <i class="nav-icon cil-cog"></i> <span>Cấu hình chung</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/bank_accounts">
                            <i class="nav-icon cil-credit-card"></i> <span>Tài khoản ngân hàng</span>
                        </a>
                    </li>
                </ul>
            </li>
            {% endif %}
            {% endif %}
        </ul>

        <!-- Removed sidebar-toggler button -->
    </div>

    <div class="wrapper d-flex flex-column min-vh-100 bg-light">
        <header class="header header-sticky mb-4">
            <div class="container-fluid">
                <!-- Top row: Menu + Logo + Icons -->
                <div class="d-flex align-items-center">
                    <!-- Left: Menu button -->
                    <button class="header-toggler px-md-0 me-3" type="button" id="headerToggler">
                        <i class="icon icon-lg cil-menu"></i>
                    </button>

                    <!-- Center: Logo -->
                    <div class="flex-grow-1 d-flex justify-content-center">
                        <a class="header-brand d-md-none" href="#" style="background: white; padding: 5px 10px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0, 198, 174, 0.1);">
                            <img src="{{ url_for('static', filename='images/logo.png') }}" alt="MIP System" style="height: 45px; width: auto;">
                        </a>
                    </div>

                    <!-- Right: All icons aligned to right -->
                    <div class="d-flex align-items-center">
                        <!-- Quick Access Icons for non-admin users -->
                        {% if session.role != 'admin' %}
                        <a class="nav-link p-2" href="/marketplace" title="Cửa Hàng">
                            <i class="icon icon-lg cil-basket" onError="this.className='icon-fallback'; this.innerHTML='🛒'"></i>
                        </a>
                        <a class="nav-link p-2" href="/marketplace/orders" title="Đơn hàng của tôi">
                            <i class="icon icon-lg cil-list-rich" onError="this.className='icon-fallback'; this.innerHTML='📋'"></i>
                        </a>
                        {% endif %}
                        <a class="nav-link p-2" href="#" title="Thông báo">
                            <i class="icon icon-lg cil-bell" onError="this.className='icon-fallback'; this.innerHTML='🔔'"></i>
                        </a>

                        <!-- User Menu -->
                        <div class="dropdown">
                            <a class="nav-link p-2" data-coreui-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                                <i class="icon icon-lg cil-user" onError="this.className='icon-fallback'; this.innerHTML='👤'"></i>
                            </a>
                            <div class="dropdown-menu dropdown-menu-end pt-0">
                                <div class="dropdown-header bg-light py-2">
                                    <div class="fw-semibold">Tài khoản</div>
                                </div>
                                <a class="dropdown-item" href="/logout">
                                    <i class="icon me-2 cil-account-logout"></i> Đăng xuất
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Bottom row: MP Balance (right aligned, mobile only) -->
                {% if session.role != 'admin' %}
                <div class="d-md-none mt-2 pb-2" style="width: 100%; display: flex !important; justify-content: flex-end !important; align-items: center !important;">
                    <div class="mp-balance-mobile">
                        <span class="fw-bold mp-text">MP: </span>
                        <span id="mp-balance" class="fw-bold mp-text">0</span>
                        <a href="/deposit" class="btn btn-sm btn-primary ms-2">
                            <i class="icon cil-credit-card" onError="this.className='icon-fallback'; this.innerHTML='💳'"></i> Nạp MP
                        </a>
                    </div>
                </div>

                <!-- Desktop MP Balance (original position) -->
                <ul class="header-nav ms-2 me-2 d-none d-md-flex">
                    <li class="nav-item">
                        <div class="nav-link">
                            <span class="fw-bold mp-text">MP: </span>
                            <span id="mp-balance-desktop" class="fw-bold mp-text">0</span>
                            <a href="/deposit" class="btn btn-sm btn-primary ms-2">
                                <i class="icon cil-credit-card"></i> Nạp MP
                            </a>
                        </div>
                    </li>
                </ul>
                {% endif %}
            </div>
        </header>

        <div class="body flex-grow-1 px-3">
            <div class="container-lg">
                {% block content %}{% endblock %}
            </div>
        </div>

        <footer class="footer">
            <div class="d-flex align-items-center">
                <img src="{{ url_for('static', filename='images/logo.png') }}" alt="MIP System" style="height: 20px; width: auto; margin-right: 8px;">
                <span>&copy; 2025 SAPMMO</span>
            </div>
        </footer>
    </div>

    <!-- CoreUI JS -->
    <script src="https://cdn.jsdelivr.net/npm/@coreui/coreui@4.2.6/dist/js/coreui.bundle.min.js"></script>

    <!-- Select2 -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

    <!-- DataTables -->
    <script type="text/javascript" src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>

    <!-- CoreUI Icons JS -->
    <script src="https://cdn.jsdelivr.net/npm/@coreui/icons@2.1.0/js/index.umd.min.js"></script>

    <!-- Custom JS -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Xử lý nút toggle sidebar
            const sidebarToggler = document.getElementById('sidebarToggler');
            const sidebar = document.getElementById('sidebar');
            const wrapper = document.querySelector('.wrapper');

            // Thêm xử lý click trực tiếp
            function toggleSidebar() {
                try {
                    // Thêm/xóa class sidebar-narrow-unfoldable
                    sidebar.classList.toggle('sidebar-narrow-unfoldable');

                    // Log để debug
                    console.log('Sidebar toggler clicked');
                    console.log('Sidebar is narrow-unfoldable:', sidebar.classList.contains('sidebar-narrow-unfoldable'));
                    console.log('Sidebar classes:', sidebar.className);

                    // Lưu trạng thái sidebar vào localStorage
                    localStorage.setItem('sidebar-narrow-unfoldable', sidebar.classList.contains('sidebar-narrow-unfoldable'));

                    // Thông báo thành công
                    console.log('Toggle sidebar successful');

                    // Thêm một chút delay để đảm bảo CSS được áp dụng
                    setTimeout(function() {
                        window.dispatchEvent(new Event('resize'));
                    }, 300);

                    return false; // Ngăn chặn sự kiện mặc định
                } catch (error) {
                    console.error('Error toggling sidebar:', error);
                }
            }

            // Gán hàm xử lý vào window để có thể gọi từ HTML
            window.toggleSidebar = toggleSidebar;

            if (sidebarToggler && sidebar) {
                // Thêm sự kiện click
                sidebarToggler.onclick = function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    toggleSidebar();
                };

                // Khôi phục trạng thái sidebar từ localStorage
                const sidebarState = localStorage.getItem('sidebar-narrow-unfoldable');
                if (sidebarState === 'true') {
                    sidebar.classList.add('sidebar-narrow-unfoldable');
                } else {
                    // Đảm bảo sidebar mở rộng mặc định
                    sidebar.classList.remove('sidebar-narrow-unfoldable');
                }

                // Đảm bảo nút toggle sidebar hoạt động đúng cách
                console.log('Sidebar toggler initialized');
            }

            // Xử lý nút toggle sidebar trên header
            const headerToggler = document.getElementById('headerToggler');

            function handleHeaderToggle(e) {
                e.preventDefault();
                e.stopPropagation();

                // Trên mobile: toggle sidebar show/hide
                if (window.innerWidth <= 768) {
                    sidebar.classList.toggle('show');
                    console.log('Mobile sidebar toggled:', sidebar.classList.contains('show'));
                }
                // Trên desktop: toggle sidebar narrow-unfoldable
                else {
                    toggleSidebar();
                    console.log('Desktop sidebar toggled narrow-unfoldable:', sidebar.classList.contains('sidebar-narrow-unfoldable'));
                }
            }

            // Gán hàm xử lý vào window để có thể gọi từ HTML
            window.handleHeaderToggle = handleHeaderToggle;

            if (headerToggler && sidebar) {
                // Thêm sự kiện click
                headerToggler.addEventListener('click', handleHeaderToggle);
            }

            // Đóng sidebar khi click ra ngoài trên mobile
            document.addEventListener('click', function(event) {
                const isClickInsideSidebar = sidebar.contains(event.target);
                const isClickInsideToggler = headerToggler && headerToggler.contains(event.target);

                if (!isClickInsideSidebar && !isClickInsideToggler && window.innerWidth <= 768) {
                    sidebar.classList.remove('show');
                }
            });

            // Xử lý resize window
            window.addEventListener('resize', function() {
                if (window.innerWidth <= 768) {
                    sidebar.classList.remove('unfoldable');
                    sidebar.classList.remove('show');
                }
            });

            // Xử lý active menu item
            const currentPath = window.location.pathname;
            const navLinks = document.querySelectorAll('.nav-link');

            // Sắp xếp links theo độ dài href giảm dần để match chính xác nhất trước
            const sortedLinks = Array.from(navLinks).sort((a, b) => {
                const hrefA = a.getAttribute('href') || '';
                const hrefB = b.getAttribute('href') || '';
                return hrefB.length - hrefA.length;
            });

            let foundMatch = false;
            sortedLinks.forEach(link => {
                const href = link.getAttribute('href');
                if (href && href !== '/' && !foundMatch) {
                    // Exact match hoặc starts with (cho sub-paths)
                    if (currentPath === href || (currentPath.startsWith(href + '/') && href.length > 1)) {
                        link.classList.add('active');
                        foundMatch = true;

                        // Nếu link nằm trong nav-group, mở rộng nav-group
                        const parentGroup = link.closest('.nav-group');
                        if (parentGroup) {
                            parentGroup.classList.add('show');
                        }
                    }
                }
            });

            // Load MP Balance - chỉ cho non-admin users
            function loadMPBalance() {
                const mpBalanceElement = document.getElementById('mp-balance');
                const mpBalanceDesktopElement = document.getElementById('mp-balance-desktop');

                if (mpBalanceElement || mpBalanceDesktopElement) {
                    fetch('/api/user/mp_balance')
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                const formattedBalance = new Intl.NumberFormat('vi-VN').format(data.mp_balance);

                                // Update mobile balance
                                if (mpBalanceElement) {
                                    mpBalanceElement.textContent = formattedBalance;
                                }

                                // Update desktop balance
                                if (mpBalanceDesktopElement) {
                                    mpBalanceDesktopElement.textContent = formattedBalance;
                                }
                            }
                        })
                        .catch(error => {
                            console.error('Error loading MP balance:', error);
                        });
                }
            }

            // Load MP Balance when page loads (chỉ nếu element tồn tại)
            loadMPBalance();

            // Handle icon loading fallback
            function setupIconFallbacks() {
                const icons = document.querySelectorAll('.header-nav i[class*="cil-"]');
                icons.forEach(icon => {
                    // Check if icon is properly loaded
                    const computedStyle = window.getComputedStyle(icon, '::before');
                    const content = computedStyle.getPropertyValue('content');

                    // If icon doesn't have content, use fallback
                    if (!content || content === 'none' || content === '""') {
                        if (icon.classList.contains('cil-basket')) {
                            icon.className = 'icon-fallback';
                            icon.innerHTML = '🛒';
                        } else if (icon.classList.contains('cil-list-rich')) {
                            icon.className = 'icon-fallback';
                            icon.innerHTML = '📋';
                        } else if (icon.classList.contains('cil-bell')) {
                            icon.className = 'icon-fallback';
                            icon.innerHTML = '🔔';
                        } else if (icon.classList.contains('cil-user')) {
                            icon.className = 'icon-fallback';
                            icon.innerHTML = '👤';
                        } else if (icon.classList.contains('cil-menu')) {
                            icon.className = 'icon-fallback';
                            icon.innerHTML = '☰';
                        } else if (icon.classList.contains('cil-credit-card')) {
                            icon.className = 'icon-fallback';
                            icon.innerHTML = '💳';
                        }
                    }
                });
            }

            // Setup icon fallbacks after a short delay to allow CSS to load
            setTimeout(setupIconFallbacks, 500);
        });
    </script>

    {% block scripts %}{% endblock %}
</body>
</html>
