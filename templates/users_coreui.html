{% extends "base_coreui.html" %}
{% block title %}<PERSON><PERSON><PERSON><PERSON> lý ng<PERSON>ờ<PERSON> dùng{% endblock %}

{% block head_extra %}
<style>
    .card {
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        border-radius: 0.25rem;
        margin-bottom: 1.5rem;
    }

    .card-header {
        background-color: #fff;
        border-bottom: 1px solid rgba(0, 0, 0, 0.125);
        padding: 0.75rem 1.25rem;
    }

    .btn-icon {
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }

    .btn-icon i {
        margin-right: 0.25rem;
    }

    .table th {
        font-weight: 500;
        background-color: #ebedef;
    }

    /* C<PERSON>i thiện giao diện Select2 */
    .select2-container--bootstrap-5 .select2-selection {
        min-height: 38px;
        border-radius: 0.25rem;
    }

    .select2-container--bootstrap-5 .select2-selection--single {
        padding: 0.375rem 0.75rem;
        height: 38px;
    }

    .select2-container--bootstrap-5 .select2-dropdown {
        border-color: #ced4da;
        border-radius: 0.25rem;
    }

    .select2-container--bootstrap-5 .select2-dropdown .select2-results__option--highlighted[aria-selected] {
        background-color: #321fdb;
    }

    /* Cải thiện giao diện form */
    .form-label {
        font-weight: 500;
        margin-bottom: 0.5rem;
    }

    /* Spinner cho loading */
    .spinner-border-sm {
        width: 1rem;
        height: 1rem;
        border-width: 0.2em;
    }
</style>
{% endblock %}

{% block content %}
<div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <strong>Danh sách người dùng</strong>
        <button class="btn btn-primary" onclick="showAddUserModal()">
            <i class="cil-plus"></i> Thêm người dùng
        </button>
    </div>
    <div class="card-body">
        <!-- Bộ lọc -->
        <div class="card mb-3">
            <div class="card-body">
                <div class="row g-3 align-items-center">
                    <div class="col-md-3">
                        <label class="form-label">Tên đăng nhập</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cil-search"></i></span>
                            <input type="text" class="form-control" id="search_username" placeholder="Tìm kiếm theo tên" value="{{ search_username }}">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Mã Unit</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cil-search"></i></span>
                            <input type="text" class="form-control" id="search_unit" placeholder="Tìm kiếm theo mã unit" value="{{ search_unit }}">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Vai trò</label>
                        <select class="form-select select2-filter" id="role_filter">
                            <option value="">Tất cả vai trò</option>
                            {% for role in roles %}
                            <option value="{{ role }}" {% if role == role_filter %}selected{% endif %}>{{ role }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Đội</label>
                        <select class="form-select select2-filter" id="team_filter">
                            <option value="">Tất cả đội</option>
                            {% for team in teams %}
                            <option value="{{ team[0] }}" {% if team[0]|string == team_filter %}selected{% endif %}>{{ team[1] }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12 text-end">
                        <button class="btn btn-primary" onclick="filterUsers(1)">
                            <i class="cil-filter"></i> Lọc
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Thông tin tổng quan -->
        <div class="mb-3">
            <div class="alert alert-info">
                <div class="d-flex align-items-center">
                    <i class="cil-info me-2"></i>
                    <div>Tổng số người dùng: <strong id="total_users">{{ total_users }}</strong></div>
                </div>
            </div>
        </div>

        <!-- Hiển thị loading -->
        <div id="loading" style="display: none; text-align: center; margin: 20px 0;">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Đang tải...</span>
            </div>
            <p>Đang tải dữ liệu...</p>
        </div>

        <!-- Bảng dữ liệu -->
        <div class="table-responsive">
            <table class="table table-striped table-hover border">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Tên đăng nhập</th>
                        <th>Vai trò</th>
                        <th>Đội</th>
                        <th>Mã Unit</th>
                        <th>Số MP</th>
                        <th>Số video</th>
                        <th>Hành động</th>
                    </tr>
                </thead>
                <tbody id="users_body">
                    {% for user in users %}
                    <tr>
                        <td>{{ user[0] }}</td>
                        <td>{{ user[1] }}</td>
                        <td>{{ user[3] }}</td>
                        <td>{{ teams|selectattr('0', 'equalto', user[4] or 0)|map(attribute='1')|first or '' }}</td>
                        <td>{{ user[5] or 'Chưa có' }}</td>
                        <td>{{ user[6] }}</td>
                        <td>{{ user[7] }}</td>
                        <td>
                            <div class="btn-group">
                                <button class="btn btn-warning btn-sm" onclick="editUser('{{ user[0] }}')">
                                    <i class="cil-pencil"></i>
                                </button>
                                <button class="btn btn-danger btn-sm" onclick="deleteUser('{{ user[0] }}')">
                                    <i class="cil-trash"></i>
                                </button>
                                {% if user_role == 'admin' %}
                                <button class="btn btn-info btn-sm" onclick="showAddMPModal('{{ user[0] }}')">
                                    <i class="cil-dollar"></i>
                                </button>
                                <button class="btn btn-secondary btn-sm" onclick="showHistoryModal('{{ user[0] }}')">
                                    <i class="cil-history"></i>
                                </button>
                                <button class="btn btn-dark btn-sm" onclick="showVideosModal('{{ user[0] }}')">
                                    <i class="cil-movie"></i>
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Phân trang -->
        <nav aria-label="Page navigation" id="pagination">
            <ul class="pagination justify-content-center">
                {% if page > 1 %}
                <li class="page-item">
                    <a class="page-link" href="#" onclick="filterUsers({{ page - 1 }}); return false;">
                        <i class="cil-chevron-left"></i>
                    </a>
                </li>
                {% endif %}
                {% for p in range(1, total_pages + 1) %}
                <li class="page-item {% if p == page %}active{% endif %}">
                    <a class="page-link" href="#" onclick="filterUsers({{ p }}); return false;">{{ p }}</a>
                </li>
                {% endfor %}
                {% if page < total_pages %}
                <li class="page-item">
                    <a class="page-link" href="#" onclick="filterUsers({{ page + 1 }}); return false;">
                        <i class="cil-chevron-right"></i>
                    </a>
                </li>
                {% endif %}
            </ul>
        </nav>
    </div>
</div>

<!-- Modal Chỉnh sửa người dùng -->
<div class="modal fade" id="editUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Chỉnh sửa người dùng</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editUserForm" method="POST" action="/edit_user">
                    <input type="hidden" name="user_id" id="edit_user_id">
                    <div class="mb-3">
                        <label class="form-label">Mã Unit</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="edit_unit_code" readonly>
                            <button type="button" class="btn btn-outline-secondary" onclick="generateUnitCode()">
                                <i class="cil-reload"></i> Generate
                            </button>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Tên đăng nhập</label>
                        <input type="text" class="form-control" name="username" id="edit_username" required
                               pattern="^[a-zA-Z0-9_]{3,20}$"
                               title="Tên đăng nhập phải từ 3-20 ký tự, chỉ chứa chữ cái, số và dấu gạch dưới">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Mật khẩu</label>
                        <input type="password" class="form-control" name="password" id="edit_password" placeholder="Để trống nếu không đổi">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Vai trò</label>
                        <select class="form-select select2-modal" name="role" id="edit_role" required>
                            <option value="admin">Admin</option>
                            <option value="leader">Đội trưởng</option>
                            <option value="member">Nhân viên</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Đội</label>
                        <select class="form-select select2-modal" name="team_id" id="edit_team_id">
                            <option value="">Chọn đội (không bắt buộc)</option>
                            {% for team in teams %}
                            <option value="{{ team[0] }}">{{ team[1] }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="text-end">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                        <button type="submit" class="btn btn-primary">Lưu</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Modal Nạp MP -->
<div class="modal fade" id="addMPModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Nạp MP</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addMPForm">
                    <input type="hidden" id="mp_user_id">
                    <div class="mb-3">
                        <label for="mp_amount" class="form-label">Số MP</label>
                        <input type="number" class="form-control" id="mp_amount" required min="1">
                    </div>
                    <div class="mb-3">
                        <label for="mp_description" class="form-label">Ghi chú</label>
                        <textarea class="form-control" id="mp_description" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                <button type="button" class="btn btn-primary" onclick="submitAddMP()">Nạp MP</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal Thêm người dùng -->
<div class="modal fade" id="addUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Thêm người dùng</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addUserForm" method="POST" action="/add_user">
                    <div class="mb-3">
                        <label class="form-label">Tên đăng nhập</label>
                        <input type="text" class="form-control" name="username" placeholder="Tên đăng nhập" required
                               pattern="^[a-zA-Z0-9_]{3,20}$"
                               title="Tên đăng nhập phải từ 3-20 ký tự, chỉ chứa chữ cái, số và dấu gạch dưới">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Mật khẩu</label>
                        <input type="password" class="form-control" name="password" placeholder="Mật khẩu" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Vai trò</label>
                        <select class="form-select select2-modal-add" name="role" id="add_role" required>
                            <option value="admin">Admin</option>
                            <option value="leader">Đội trưởng</option>
                            <option value="member" selected>Nhân viên</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Đội</label>
                        <select class="form-select select2-modal-add" name="team_id" id="add_team_id">
                            <option value="">Chọn đội (không bắt buộc)</option>
                            {% for team in teams %}
                            <option value="{{ team[0] }}">{{ team[1] }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="text-end">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="cil-plus"></i> Thêm
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Modal Lịch sử MP -->
<div class="modal fade" id="mpHistoryModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Lịch sử MP</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Thời gian</th>
                                <th>Số MP</th>
                                <th>Loại</th>
                                <th>Ghi chú</th>
                                <th>Người thực hiện</th>
                            </tr>
                        </thead>
                        <tbody id="mpHistoryBody">
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
        // Khởi tạo Select2 cho dropdown
        $('.select2-dropdown').select2({
            theme: 'bootstrap-5',
            width: '100%',
            placeholder: "Chọn...",
            allowClear: true
        });

        // Khởi tạo Select2 cho dropdown trong modal chỉnh sửa
        $('.select2-modal').select2({
            theme: 'bootstrap-5',
            width: '100%',
            dropdownParent: $('#editUserModal')
        });

        // Khởi tạo Select2 cho dropdown trong modal thêm mới
        $('.select2-modal-add').select2({
            theme: 'bootstrap-5',
            width: '100%',
            dropdownParent: $('#addUserModal')
        });
    });

    function showAddUserModal() {
        // Reset form
        $('#addUserForm')[0].reset();

        // Reset Select2
        $('#add_role').val('member').trigger('change');
        $('#add_team_id').val('').trigger('change');

        // Hiển thị modal
        new coreui.Modal(document.getElementById('addUserModal')).show();
    }

    function editUser(userId) {
        fetch(`/get_user/${userId}`)
            .then(response => response.json())
            .then(data => {
                document.getElementById('edit_user_id').value = data.user_id;
                document.getElementById('edit_username').value = data.username;
                document.getElementById('edit_unit_code').value = data.unit_code || '';

                // Thiết lập giá trị cho Select2
                $('#edit_role').val(data.role).trigger('change');
                $('#edit_team_id').val(data.team_id || '').trigger('change');

                // Hiển thị modal
                new coreui.Modal(document.getElementById('editUserModal')).show();
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Có lỗi xảy ra khi lấy dữ liệu người dùng');
            });
    }

    function generateUnitCode() {
        const userId = document.getElementById('edit_user_id').value;
        const form = document.getElementById('editUserForm');
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'generate_unit_code';
        input.value = '1';
        form.appendChild(input);
        form.submit();
    }

    function deleteUser(userId) {
        if (confirm('Bạn có chắc muốn xóa người dùng này không?')) {
            fetch(`/delete_user/${userId}`, { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.message) {
                        alert(data.message);
                        location.reload();
                    }
                })
                .catch(error => {
                    alert("Lỗi: " + error);
                });
        }
    }

    function showAddMPModal(userId) {
        $('#mp_user_id').val(userId);
        $('#mp_amount').val('');
        $('#mp_description').val('');
        new coreui.Modal(document.getElementById('addMPModal')).show();
    }

    function submitAddMP() {
        const userId = $('#mp_user_id').val();
        const amount = $('#mp_amount').val();
        const description = $('#mp_description').val();

        if (!amount) {
            alert('Vui lòng nhập số MP');
            return;
        }

        // Disable nút submit và hiển thị loading
        const submitBtn = $('.modal-footer .btn-primary');
        const originalText = submitBtn.text();
        submitBtn.prop('disabled', true);
        submitBtn.html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Đang xử lý...');

        fetch('/api/user/mp/add', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                user_id: userId,
                amount: parseInt(amount),
                description: description
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                throw new Error(data.error);
            }
            alert('Nạp MP thành công');
            coreui.Modal.getInstance(document.getElementById('addMPModal')).hide();
            location.reload();
        })
        .catch(error => {
            alert('Có lỗi xảy ra: ' + error.message);
        })
        .finally(() => {
            // Restore nút submit
            submitBtn.prop('disabled', false);
            submitBtn.html(originalText);
        });
    }

    function showHistoryModal(userId) {
        $.ajax({
            url: '/api/user/mp/history/' + userId,
            method: 'GET',
            success: function(response) {
                const tbody = $('#mpHistoryBody');
                tbody.empty();

                if (response.length === 0) {
                    tbody.append('<tr><td colspan="5" class="text-center">Không có lịch sử giao dịch</td></tr>');
                } else {
                    response.forEach(function(t) {
                        tbody.append(`
                            <tr>
                                <td>${t.created_at ? (typeof t.created_at === 'string' ? new Date(t.created_at).toLocaleString() : t.created_at) : ''}</td>
                                <td>${t.amount}</td>
                                <td>${t.type === 'ADD' ? 'Nạp' : 'Tiêu'}</td>
                                <td>${t.description || ''}</td>
                                <td>${t.created_by_name}</td>
                            </tr>
                        `);
                    });
                }

                new coreui.Modal(document.getElementById('mpHistoryModal')).show();
            },
            error: function(xhr) {
                alert('Có lỗi xảy ra: ' + (xhr.responseJSON?.error || 'Không thể tải lịch sử'));
            }
        });
    }

    function showVideosModal(userId) {
        alert('Chức năng đang được phát triển');
        // Implement khi có API và modal tương ứng
    }

    function showLoading() {
        document.getElementById('loading').style.display = 'block';
    }

    function hideLoading() {
        document.getElementById('loading').style.display = 'none';
    }

    function filterUsers(page) {
        showLoading();
        const searchUsername = document.getElementById('search_username').value;
        const searchUnit = document.getElementById('search_unit').value;
        const roleFilter = document.getElementById('role_filter').value;
        const teamFilter = document.getElementById('team_filter').value;

        fetch(`/users?page=${page}&search_username=${encodeURIComponent(searchUsername)}&search_unit=${encodeURIComponent(searchUnit)}&role=${encodeURIComponent(roleFilter)}&team_id=${encodeURIComponent(teamFilter)}`, {
            headers: { 'X-Requested-With': 'XMLHttpRequest' }
        })
        .then(response => response.json())
        .then(data => {
            let html = '';
            data.users.forEach(user => {
                const teamName = getTeamName(user.team_id);
                html += `
                    <tr>
                        <td>${user.user_id}</td>
                        <td>${user.username}</td>
                        <td>${user.role}</td>
                        <td>${teamName}</td>
                        <td>${user.unit_code || 'Chưa có'}</td>
                        <td>${user.mp_balance}</td>
                        <td>${user.total_videos}</td>
                        <td>
                            <div class="btn-group">
                                <button class="btn btn-warning btn-sm" onclick="editUser('${user.user_id}')">
                                    <i class="cil-pencil"></i>
                                </button>
                                <button class="btn btn-danger btn-sm" onclick="deleteUser('${user.user_id}')">
                                    <i class="cil-trash"></i>
                                </button>
                                ${user_role === 'admin' ? `
                                <button class="btn btn-info btn-sm" onclick="showAddMPModal('${user.user_id}')">
                                    <i class="cil-dollar"></i>
                                </button>
                                <button class="btn btn-secondary btn-sm" onclick="showHistoryModal('${user.user_id}')">
                                    <i class="cil-history"></i>
                                </button>
                                <button class="btn btn-dark btn-sm" onclick="showVideosModal('${user.user_id}')">
                                    <i class="cil-movie"></i>
                                </button>
                                ` : ''}
                            </div>
                        </td>
                    </tr>`;
            });
            document.getElementById('users_body').innerHTML = html;
            document.getElementById('total_users').innerText = data.total_users;

            let paginationHtml = '';
            if (data.page > 1) {
                paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="filterUsers(${data.page - 1}); return false;"><i class="cil-chevron-left"></i></a></li>`;
            }
            for (let p = 1; p <= data.total_pages; p++) {
                paginationHtml += `<li class="page-item ${p === data.page ? 'active' : ''}"><a class="page-link" href="#" onclick="filterUsers(${p}); return false;">${p}</a></li>`;
            }
            if (data.page < data.total_pages) {
                paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="filterUsers(${data.page + 1}); return false;"><i class="cil-chevron-right"></i></a></li>`;
            }
            document.getElementById('pagination').innerHTML = `<ul class="pagination justify-content-center">${paginationHtml}</ul>`;

            hideLoading();
        })
        .catch(error => {
            alert("Lỗi khi lọc người dùng: " + error.message);
            hideLoading();
        });
    }

    // Hàm lấy tên đội từ dữ liệu ban đầu - sử dụng global variables
    if (typeof window.teams === 'undefined') {
        window.teams = {{ teams|tojson }};
    }
    const user_role = "{{ user_role }}";

    function getTeamName(teamId) {
        if (!teamId) return '';
        const team = window.teams.find(t => t[0] == teamId);
        return team ? team[1] : '';
    }
</script>
{% endblock %}