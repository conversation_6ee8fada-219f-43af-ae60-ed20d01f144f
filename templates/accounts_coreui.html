{% extends "base_coreui.html" %}
{% block title %}Quản lý tài khoản{% endblock %}

{% block head_extra %}
<style>
    .card {
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        border-radius: 0.25rem;
        margin-bottom: 1.5rem;
    }

    .card-header {
        background-color: #fff;
        border-bottom: 1px solid rgba(0, 0, 0, 0.125);
        padding: 0.75rem 1.25rem;
    }

    .btn-icon {
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }

    .btn-icon i {
        margin-right: 0.25rem;
    }

    .spinning {
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .table th {
        font-weight: 500;
        background-color: #ebedef;
    }

    .badge {
        font-size: 0.75rem;
        font-weight: 500;
        padding: 0.25em 0.5em;
    }
</style>
{% endblock %}

{% block content %}
<div class="card mb-4">
    <div class="card-header">
        <strong><PERSON>u<PERSON>n lý tài khoản</strong>
    </div>
    <div class="card-body">
        <!-- Nút Thêm mới tài khoản -->
        <div class="mb-3">
            <button class="btn btn-primary" onclick="new coreui.Modal(document.getElementById('addAccountModal')).show()">
                <i class="cil-plus"></i> Thêm mới tài khoản
            </button>
        </div>

        <!-- Bộ lọc -->
        <div class="card mb-3">
            <div class="card-body">
                <div class="row g-3 align-items-center">
                    <div class="col-md-3">
                        <div class="input-group">
                            <span class="input-group-text"><i class="cil-search"></i></span>
                            <input type="text" class="form-control" id="search_name" placeholder="Tìm kiếm theo tên" value="{{ search_name }}">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="status_filter">
                            <option value="">Tất cả trạng thái</option>
                            {% for status in statuses %}
                            <option value="{{ status }}" {% if status == status_filter %}selected{% endif %}>{{ status }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="team_filter">
                            <option value="">Tất cả đội</option>
                            {% for team in teams %}
                            <option value="{{ team[0] }}" {% if team[0]|string == team_filter %}selected{% endif %}>{{ team[1] }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex gap-2">
                            <button class="btn btn-primary" onclick="filterAccounts(1)">
                                <i class="cil-filter"></i> Lọc
                            </button>
                            <button class="btn btn-info" id="reloadDataBtn" onclick="reloadData()">
                                <i class="cil-reload"></i> Reload Data
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Thông tin tổng quan -->
        <div class="mb-3">
            <div class="alert alert-info">
                <div class="d-flex align-items-center">
                    <i class="cil-info me-2"></i>
                    <div>Tổng số tài khoản: <strong>{{ overall_account_count }}</strong> | Số tài khoản sau lọc: <strong id="filtered_count">{{ total_accounts }}</strong></div>
                </div>
            </div>
        </div>

        <!-- Hiển thị loading -->
        <div id="loading" style="display: none; text-align: center; margin: 20px 0;">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Đang tải...</span>
            </div>
            <p>Đang tải dữ liệu...</p>
        </div>

        <!-- Bảng dữ liệu -->
        <div class="table-responsive">
            <table class="table table-striped table-hover border" id="accounts_table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Tên</th>
                        <th>Đội</th>
                        <th>Người phụ trách</th>
                        <th>Trạng thái</th>
                        <th>Follower</th>
                        <th>Likes</th>
                        <th>Total Videos</th>
                        <th>Cho thuê</th>
                        <th>Hành động</th>
                    </tr>
                </thead>
                <tbody id="accounts_body">
                    {% for account in accounts %}
                    <tr>
                        <td>{{ account[0] }}</td>
                        <td>{{ account[1] }}</td>
                        <td>{{ teams|selectattr('0', 'equalto', account[2] or 0)|map(attribute='1')|first or '' }}</td>
                        <td>{{ users|selectattr('0', 'equalto', account[3] or 0)|map(attribute='1')|first or '' }}</td>
                        <td>{{ account[4] }}</td>
                        <td>{{ account[5] }}</td>
                        <td>{{ account[6] }}</td>
                        <td>{{ account[7] }}</td>
                        <td>
                            {% if account[8] %}
                            <span class="badge bg-success">Có thể thuê</span>
                            {% else %}
                            <span class="badge bg-secondary">Không thể thuê</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group">
                                <button class="btn btn-warning btn-sm" onclick="editAccount('{{ account[0] }}')">
                                    <i class="cil-pencil"></i>
                                </button>
                                <button class="btn btn-info btn-sm" onclick="refreshAccount('{{ account[0] }}')" title="Refresh Data">
                                    <i class="cil-reload"></i>
                                </button>
                                <button class="btn btn-danger btn-sm" onclick="deleteAccount('{{ account[0] }}')">
                                    <i class="cil-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Phân trang -->
        <nav aria-label="Page navigation" id="pagination">
            <ul class="pagination justify-content-center">
                {% if page > 1 %}
                <li class="page-item">
                    <a class="page-link" href="#" onclick="filterAccounts({{ page - 1 }}); return false;">
                        <i class="cil-chevron-left"></i>
                    </a>
                </li>
                {% endif %}
                {% for p in range(1, total_pages + 1) %}
                <li class="page-item {% if p == page %}active{% endif %}">
                    <a class="page-link" href="#" onclick="filterAccounts({{ p }}); return false;">{{ p }}</a>
                </li>
                {% endfor %}
                {% if page < total_pages %}
                <li class="page-item">
                    <a class="page-link" href="#" onclick="filterAccounts({{ page + 1 }}); return false;">
                        <i class="cil-chevron-right"></i>
                    </a>
                </li>
                {% endif %}
            </ul>
        </nav>
    </div>
</div>
<!-- Modal Thêm tài khoản -->
<div class="modal fade" id="addAccountModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Thêm tài khoản TikTok</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addAccountForm" method="POST" action="/add_account">
                    <div class="mb-3">
                        <label class="form-label">Tên tài khoản</label>
                        <input type="text" class="form-control" name="account_name" placeholder="Tên tài khoản" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Cookie Data</label>
                        <textarea class="form-control" name="cookie_data" placeholder="Cookie Data" required rows="5"></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Đội</label>
                        <select class="form-select" name="team_id" id="add_team_id">
                            <option value="">Chọn đội (không bắt buộc)</option>
                            {% for team in teams %}
                            <option value="{{ team[0] }}">{{ team[1] }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Người phụ trách</label>
                        <select class="form-select" name="assigned_user_id" id="add_assigned_user_id">
                            <option value="">Chọn người phụ trách (không bắt buộc)</option>
                            {% for user in users %}
                            <option value="{{ user[0] }}">{{ user[1] }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="add_proxy_id" class="form-label">Proxy</label>
                        <select class="form-select" name="proxy_id" id="add_proxy_id">
                            <option value="">Chọn proxy (không bắt buộc)</option>
                            {% for proxy in proxies %}
                            <option value="{{ proxy[0] }}">{{ proxy[1] }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="add_status" class="form-label">Trạng thái</label>
                        <select class="form-select" name="status" id="add_status" required>
                            <option value="Live">Live</option>
                            <option value="Die">Die</option>
                            <option value="Not Available" selected>Not Available</option>
                            <option value="AFF">AFF</option>
                            <option value="Đang nuôi">Đang nuôi</option>
                            <option value="Đủ điều kiện">Đủ điều kiện</option>
                            <option value="Bật hụt">Bật hụt</option>
                            <option value="Thu giỏ">Thu giỏ</option>
                        </select>
                    </div>
                    <div class="text-end">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                        <button type="submit" class="btn btn-primary">Thêm</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Modal Chỉnh sửa tài khoản -->
<div class="modal fade" id="editAccountModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Chỉnh sửa tài khoản</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editAccountForm" method="POST" action="/edit_account">
                    <input type="hidden" name="account_id" id="edit_account_id">
                    <div class="mb-3">
                        <label class="form-label">Tên tài khoản</label>
                        <input type="text" class="form-control" name="account_name" id="edit_account_name" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Cookie Data</label>
                        <textarea class="form-control" name="cookie_data" id="edit_cookie_data" required rows="5"></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Đội</label>
                        <select class="form-select" name="team_id" id="edit_team_id">
                            <option value="">Chọn đội (không bắt buộc)</option>
                            {% for team in teams %}
                            <option value="{{ team[0] }}">{{ team[1] }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Người phụ trách</label>
                        <select class="form-select" name="assigned_user_id" id="edit_assigned_user_id">
                            <option value="">Chọn người phụ trách (không bắt buộc)</option>
                            {% for user in users %}
                            <option value="{{ user[0] }}">{{ user[1] }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="edit_proxy_id" class="form-label">Proxy</label>
                        <select class="form-select" name="proxy_id" id="edit_proxy_id">
                            <option value="">Chọn proxy (không bắt buộc)</option>
                            {% for proxy in proxies %}
                            <option value="{{ proxy[0] }}">{{ proxy[1] }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="edit_status" class="form-label">Trạng thái</label>
                        <select class="form-select" name="status" id="edit_status" required>
                            <option value="Live">Live</option>
                            <option value="Die">Die</option>
                            <option value="Not Available">Not Available</option>
                            <option value="AFF">AFF</option>
                            <option value="Đang nuôi">Đang nuôi</option>
                            <option value="Đủ điều kiện">Đủ điều kiện</option>
                            <option value="Bật hụt">Bật hụt</option>
                            <option value="Thu giỏ">Thu giỏ</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" name="is_rentable" id="edit_is_rentable" value="1">
                            <label class="form-check-label" for="edit_is_rentable">Có thể thuê</label>
                        </div>
                    </div>
                    <div class="text-end">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                        <button type="submit" class="btn btn-primary">Lưu</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
        // Select2 cho modal Thêm tài khoản
        $('#add_team_id').select2({
            placeholder: "Chọn đội (không bắt buộc)",
            allowClear: true,
            width: '100%',
            dropdownParent: $('#addAccountModal')
        });
        $('#add_assigned_user_id').select2({
            placeholder: "Chọn người phụ trách (không bắt buộc)",
            allowClear: true,
            width: '100%',
            dropdownParent: $('#addAccountModal')
        });
        $('#add_proxy_id').select2({
            placeholder: "Chọn proxy (không bắt buộc)",
            allowClear: true,
            width: '100%',
            dropdownParent: $('#addAccountModal')
        });
        $('#add_status').select2({
            placeholder: "Chọn trạng thái",
            allowClear: true,
            width: '100%',
            dropdownParent: $('#addAccountModal')
        });

        // Select2 cho modal Chỉnh sửa tài khoản
        $('#edit_team_id').select2({
            placeholder: "Chọn đội (không bắt buộc)",
            allowClear: true,
            width: '100%',
            dropdownParent: $('#editAccountModal')
        });
        $('#edit_assigned_user_id').select2({
            placeholder: "Chọn người phụ trách (không bắt buộc)",
            allowClear: true,
            width: '100%',
            dropdownParent: $('#editAccountModal')
        });
        $('#edit_proxy_id').select2({
            placeholder: "Chọn proxy (không bắt buộc)",
            allowClear: true,
            width: '100%',
            dropdownParent: $('#editAccountModal')
        });
        $('#edit_status').select2({
            placeholder: "Chọn trạng thái",
            allowClear: true,
            width: '100%',
            dropdownParent: $('#editAccountModal')
        });

        // Select2 cho bộ lọc
        $('#status_filter').select2({
            placeholder: "Tất cả trạng thái",
            allowClear: true,
            width: '100%'
        });
        $('#team_filter').select2({
            placeholder: "Tất cả đội",
            allowClear: true,
            width: '100%'
        });
    });

    function showLoading() {
        document.getElementById('loading').style.display = 'block';
    }

    function hideLoading() {
        document.getElementById('loading').style.display = 'none';
    }

    function editAccount(accountId) {
        fetch(`/get_account/${accountId}`)
            .then(response => response.json())
            .then(data => {
                console.log('Account data from server:', data);

                // Reset form
                $('#editAccountForm')[0].reset();

                // Set basic fields
                document.getElementById('edit_account_id').value = data.account_id;
                document.getElementById('edit_account_name').value = data.account_name;
                document.getElementById('edit_cookie_data').value = data.cookie_data;
                document.getElementById('edit_is_rentable').checked = data.is_rentable;

                // Lưu proxy_id để sử dụng sau khi modal được hiển thị
                window.currentProxyId = data.proxy_id || '';
                console.log('Saved proxy_id for later use:', window.currentProxyId);
                console.log('Type of proxy_id:', typeof window.currentProxyId);

                // Nếu proxy_id là số, chuyển đổi thành chuỗi
                if (typeof window.currentProxyId === 'number') {
                    window.currentProxyId = window.currentProxyId.toString();
                    console.log('Converted proxy_id to string:', window.currentProxyId);
                }

                // Set Select2 fields
                $('#edit_team_id').val(data.team_id || '').trigger('change');
                $('#edit_assigned_user_id').val(data.assigned_user_id || '').trigger('change');
                $('#edit_status').val(data.status).trigger('change');

                // Show modal
                new coreui.Modal(document.getElementById('editAccountModal')).show();
            })
            .catch(error => {
                console.error('Error fetching account data:', error);
                alert('Có lỗi xảy ra khi lấy dữ liệu tài khoản');
            });
    }

    // Xử lý form submit
    $('#editAccountForm').on('submit', function(e) {
        e.preventDefault();

        // Lấy giá trị proxy_id
        let proxyId = $('#edit_proxy_id').val();
        console.log('Raw proxy_id from select:', proxyId);

        // Nếu không có giá trị hoặc giá trị rỗng, đặt thành chuỗi rỗng
        if (!proxyId) {
            proxyId = '';
            console.log('Empty proxy_id detected, setting to empty string');
        }

        // Get all form data
        const formData = {
            account_id: $('#edit_account_id').val(),
            account_name: $('#edit_account_name').val(),
            cookie_data: $('#edit_cookie_data').val(),
            team_id: $('#edit_team_id').val() || '',
            assigned_user_id: $('#edit_assigned_user_id').val() || '',
            status: $('#edit_status').val(),
            proxy_id: proxyId,
            is_rentable: $('#edit_is_rentable').is(':checked') ? '1' : '0'
        };

        // Log detailed information
        console.log('Form data to be submitted:', formData);
        console.log('Proxy ID element:', document.getElementById('edit_proxy_id'));
        console.log('Select2 data:', $('#edit_proxy_id').select2('data'));

        $.ajax({
            url: '/edit_account',
            method: 'POST',
            data: formData,
            success: function(response) {
                if (response.error) {
                    alert(response.error);
                } else {
                    // Log debug info
                    if (response.debug) {
                        console.log('Debug info:', response.debug);
                    }

                    alert(response.message || 'Cập nhật tài khoản thành công');
                    coreui.Modal.getInstance(document.getElementById('editAccountModal')).hide();
                    window.location.reload();
                }
            },
            error: function(xhr) {
                console.error('Error:', xhr);
                if (xhr.responseJSON && xhr.responseJSON.error) {
                    alert(xhr.responseJSON.error);
                } else {
                    alert('Có lỗi xảy ra khi cập nhật tài khoản');
                }
            }
        });
    });

    // Khởi tạo Select2 cho proxy khi modal được mở
    $('#editAccountModal').on('shown.bs.modal', function () {
        console.log('Modal shown, initializing Select2');

        // Khởi tạo Select2
        $('#edit_proxy_id').select2({
            placeholder: "Chọn proxy (không bắt buộc)",
            allowClear: true,
            width: '100%',
            dropdownParent: $('#editAccountModal')
        }).on('select2:open', function() {
            console.log('Select2 opened');
        });

        // Thiết lập giá trị sau khi Select2 đã được khởi tạo
        if (window.currentProxyId) {
            console.log('Setting proxy_id after modal shown:', window.currentProxyId);

            // Sử dụng setTimeout để đảm bảo Select2 đã được khởi tạo hoàn toàn
            setTimeout(function() {
                console.log('Attempting to set proxy_id value now');
                $('#edit_proxy_id').val(window.currentProxyId).trigger('change');

                // Kiểm tra xem giá trị đã được thiết lập chưa
                console.log('Current proxy_id value after setting:', $('#edit_proxy_id').val());
            }, 300);
        }
    });

    function deleteAccount(accountId) {
        if (confirm('Bạn có chắc muốn xóa tài khoản này không?')) {
            fetch(`/delete_account/${accountId}`, { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.message) {
                        alert(data.message);
                        filterAccounts(1);  // Cập nhật bảng sau khi xóa
                    }
                })
                .catch(error => {
                    alert("Lỗi: " + error);
                });
        }
    }

    let isReloading = false;
    function reloadData() {
        if (isReloading) {
            alert("Đang tải dữ liệu, vui lòng đợi...");
            return;
        }
        isReloading = true;
        document.getElementById('reloadDataBtn').disabled = true;
        document.getElementById('reloadDataBtn').innerHTML = '<i class="cil-reload spinning"></i> Đang tải...';

        fetch('/update_account_stats', {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                alert(data.error);
            } else {
                alert(data.message);
                filterAccounts(1);  // Cập nhật bảng sau khi reload
            }
        })
        .catch(error => {
            alert("Lỗi khi tải dữ liệu: " + error.message);
        })
        .finally(() => {
            isReloading = false;
            document.getElementById('reloadDataBtn').disabled = false;
            document.getElementById('reloadDataBtn').innerHTML = '<i class="cil-reload"></i> Reload Data';
        });
    }

    // Function để cập nhật một dòng account trong table
    function updateSingleAccountInTable(account) {
        // Tìm dòng trong table theo account_id
        const tableBody = document.getElementById('accounts_body');
        const rows = tableBody.querySelectorAll('tr');

        for (let row of rows) {
            const firstCell = row.querySelector('td');
            if (firstCell && firstCell.textContent.trim() === account.account_id.toString()) {
                // Cập nhật các cell trong dòng này
                const cells = row.querySelectorAll('td');
                if (cells.length >= 7) {
                    // cells[0] = ID (không đổi)
                    // cells[1] = Tên (không đổi)
                    // cells[2] = Đội (có thể đổi)
                    cells[2].textContent = account.team_name || '';
                    // cells[3] = Người phụ trách (có thể đổi)
                    cells[3].textContent = account.assigned_user || '';
                    // cells[4] = Trạng thái (có thể đổi)
                    cells[4].textContent = account.status || '';
                    // cells[5] = Follower (cập nhật)
                    cells[5].textContent = (account.follower_count || 0).toLocaleString();
                    // cells[6] = Likes (cập nhật)
                    cells[6].textContent = (account.like_count || 0).toLocaleString();
                    // cells[7] = Total Videos (cập nhật)
                    cells[7].textContent = account.total_videos || 0;
                    // cells[8] và cells[9] = Cho thuê và Hành động (không đổi)
                }

                // Thêm hiệu ứng highlight để user biết dòng đã được cập nhật
                row.style.backgroundColor = '#d4edda';
                setTimeout(() => {
                    row.style.backgroundColor = '';
                }, 2000);

                break;
            }
        }
    }

    let isRefreshing = {};
    function refreshAccount(accountId) {
        if (isRefreshing[accountId]) {
            alert("Đang tải dữ liệu cho tài khoản này, vui lòng đợi...");
            return;
        }
        isRefreshing[accountId] = true;
        const refreshBtn = document.querySelector(`button[onclick="refreshAccount('${accountId}')"]`);
        refreshBtn.disabled = true;
        refreshBtn.innerHTML = '<i class="cil-reload spinning"></i>';

        fetch(`/update_single_account_stats/${accountId}`, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                alert(data.error);
            } else {
                alert(data.message);

                // Cập nhật dòng cụ thể thay vì reload toàn bộ table
                if (data.updated_account) {
                    updateSingleAccountInTable(data.updated_account);
                } else {
                    // Fallback: reload table nếu không có dữ liệu
                    filterAccounts(1);
                }
            }
        })
        .catch(error => {
            alert("Lỗi khi tải dữ liệu: " + error.message);
        })
        .finally(() => {
            isRefreshing[accountId] = false;
            refreshBtn.disabled = false;
            refreshBtn.innerHTML = '<i class="cil-reload"></i>';
        });
    }

    function filterAccounts(page) {
        showLoading();
        const searchName = document.getElementById('search_name').value;
        const statusFilter = document.getElementById('status_filter').value;
        const teamFilter = document.getElementById('team_filter').value;

        fetch(`/accounts?page=${page}&search_name=${encodeURIComponent(searchName)}&status=${encodeURIComponent(statusFilter)}&team_id=${encodeURIComponent(teamFilter)}`, {
            headers: { 'X-Requested-With': 'XMLHttpRequest' }
        })
        .then(response => response.json())
        .then(data => {
            let html = '';
            data.accounts.forEach(account => {
                html += `
                    <tr>
                        <td>${account.account_id}</td>
                        <td>${account.account_name}</td>
                        <td>${getTeamName(account.team_id)}</td>
                        <td>${getUserName(account.assigned_user_id)}</td>
                        <td>${account.status}</td>
                        <td>${account.follower_count}</td>
                        <td>${account.like_count}</td>
                        <td>${account.total_videos}</td>
                        <td>
                            ${account.is_rentable ?
                                '<span class="badge bg-success">Có thể thuê</span>' :
                                '<span class="badge bg-secondary">Không thể thuê</span>'
                            }
                        </td>
                        <td>
                            <div class="btn-group">
                                <button class="btn btn-warning btn-sm" onclick="editAccount('${account.account_id}')">
                                    <i class="cil-pencil"></i>
                                </button>
                                <button class="btn btn-info btn-sm" onclick="refreshAccount('${account.account_id}')" title="Refresh Data">
                                    <i class="cil-reload"></i>
                                </button>
                                <button class="btn btn-danger btn-sm" onclick="deleteAccount('${account.account_id}')">
                                    <i class="cil-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>`;
            });
            document.getElementById('accounts_body').innerHTML = html;

            document.getElementById('filtered_count').innerText = data.total_accounts;

            let paginationHtml = '';
            if (data.page > 1) {
                paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="filterAccounts(${data.page - 1}); return false;"><i class="cil-chevron-left"></i></a></li>`;
            }
            for (let p = 1; p <= data.total_pages; p++) {
                paginationHtml += `<li class="page-item ${p === data.page ? 'active' : ''}"><a class="page-link" href="#" onclick="filterAccounts(${p}); return false;">${p}</a></li>`;
            }
            if (data.page < data.total_pages) {
                paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="filterAccounts(${data.page + 1}); return false;"><i class="cil-chevron-right"></i></a></li>`;
            }
            document.getElementById('pagination').innerHTML = `<ul class="pagination justify-content-center">${paginationHtml}</ul>`;

            hideLoading();
        })
        .catch(error => {
            alert("Lỗi khi lọc tài khoản: " + error.message);
            hideLoading();
        });
    }

    // Hàm lấy tên đội và người dùng từ dữ liệu ban đầu
    const teams = {{ teams|tojson }};
    const users = {{ users|tojson }};

    function getTeamName(teamId) {
        const team = teams.find(t => t[0] === teamId);
        return team ? team[1] : '';
    }

    function getUserName(userId) {
        const user = users.find(u => u[0] === userId);
        return user ? user[1] : '';
    }
</script>
{% endblock %}