{% extends "base_coreui.html" %}
{% block title %}Chi tiết đơn hàng - Marketplace{% endblock %}

{% block head_extra %}
<style>
    .order-header {
        background: linear-gradient(135deg, #00C6AE, #00a693);
        color: white;
        border-radius: 12px;
        padding: 25px;
        margin-bottom: 25px;
    }
    .product-card {
        border: 1px solid #e0e0e0;
        border-radius: 12px;
        margin-bottom: 20px;
        transition: all 0.3s ease;
    }
    .product-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    .file-item {
        display: flex;
        align-items: center;
        padding: 15px;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        margin-bottom: 12px;
        background: white;
        transition: all 0.3s ease;
    }
    .file-item:hover {
        border-color: #00C6AE;
        box-shadow: 0 2px 8px rgba(0,198,174,0.1);
        transform: translateY(-1px);
    }
    .file-icon {
        width: 50px;
        height: 50px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        font-size: 20px;
        color: white;
    }
    .file-icon.image { background: linear-gradient(135deg, #28a745, #20c997); }
    .file-icon.document { background: linear-gradient(135deg, #dc3545, #e83e8c); }
    .file-icon.video { background: linear-gradient(135deg, #6f42c1, #6610f2); }
    .file-icon.audio { background: linear-gradient(135deg, #fd7e14, #ffc107); }
    .file-icon.other { background: linear-gradient(135deg, #6c757d, #495057); }
    .download-btn {
        background: linear-gradient(135deg, #00C6AE, #00a693);
        border: none;
        color: white;
        padding: 8px 16px;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 6px;
    }
    .download-btn:hover {
        background: linear-gradient(135deg, #00a693, #008a7a);
        color: white;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0,198,174,0.3);
    }
    .account-item {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 12px;
        margin-bottom: 8px;
        border-left: 4px solid #00C6AE;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="order-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h3 class="mb-2"><i class="cil-list"></i> Chi tiết đơn hàng</h3>
                <p class="mb-0 opacity-75" id="orderNumber">Đang tải...</p>
            </div>
            <div>
                <a href="/marketplace/orders" class="btn btn-light">
                    <i class="cil-arrow-left"></i> Quay lại
                </a>
            </div>
        </div>
    </div>

    <!-- Order Info -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="cil-info"></i> Thông tin đơn hàng</h5>
                </div>
                <div class="card-body" id="orderInfo">
                    <div class="text-center py-4">
                        <i class="cil-reload fa-spin fa-2x text-muted"></i>
                        <p class="text-muted mt-2">Đang tải thông tin...</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="cil-chart-pie"></i> Tổng quan</h5>
                </div>
                <div class="card-body" id="orderSummary">
                    <div class="text-center py-4">
                        <i class="cil-reload fa-spin fa-2x text-muted"></i>
                        <p class="text-muted mt-2">Đang tải tổng quan...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Products -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0"><i class="cil-basket"></i> Sản phẩm đã mua</h5>
        </div>
        <div class="card-body">
            <div id="orderProducts">
                <div class="text-center py-4">
                    <i class="cil-reload fa-spin fa-2x text-muted"></i>
                    <p class="text-muted mt-2">Đang tải sản phẩm...</p>
                </div>
            </div>
        </div>
    </div>
</div>



<script>
const orderId = {{ order_id }};
let orderData = null;

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    loadOrderDetail();
});

function loadOrderDetail() {
    fetch(`/api/marketplace/orders/${orderId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                orderData = data.order;
                displayOrderDetail(data.order);
            } else {
                showError(data.error);
            }
        })
        .catch(error => {
            console.error('Error loading order:', error);
            showError('Lỗi kết nối');
        });
}

function displayOrderDetail(order) {
    // Update header
    document.getElementById('orderNumber').textContent = `Đơn hàng #${order.order_number}`;

    // Order info
    document.getElementById('orderInfo').innerHTML = `
        <table class="table table-borderless">
            <tr><td><strong>Mã đơn hàng:</strong></td><td>#${order.order_number}</td></tr>
            <tr><td><strong>Ngày đặt:</strong></td><td>${formatDate(order.created_at)}</td></tr>
            <tr><td><strong>Trạng thái:</strong></td><td><span class="badge ${getStatusClass(order.status)}">${getStatusText(order.status)}</span></td></tr>
            ${order.completed_at ? `<tr><td><strong>Hoàn thành:</strong></td><td>${formatDate(order.completed_at)}</td></tr>` : ''}
        </table>
    `;
    
    // Order summary
    document.getElementById('orderSummary').innerHTML = `
        <table class="table table-borderless">
            <tr><td><strong>Tạm tính:</strong></td><td>${order.total_amount.toLocaleString()} MP</td></tr>
            ${order.discount_amount > 0 ? `<tr><td><strong>Giảm giá:</strong></td><td class="text-success">-${order.discount_amount.toLocaleString()} MP</td></tr>` : ''}
            <tr><td><strong>Tổng cộng:</strong></td><td class="text-primary fw-bold">${order.final_amount.toLocaleString()} MP</td></tr>
            <tr><td><strong>Số sản phẩm:</strong></td><td>${order.items.length} sản phẩm</td></tr>
        </table>
    `;
    
    // Products
    displayProducts(order.items);
}

function displayProducts(items) {
    const container = document.getElementById('orderProducts');
    
    container.innerHTML = items.map(item => `
        <div class="product-card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-1">${item.product_name}</h6>
                        <span class="badge bg-info">${getProductTypeLabel(item.product_type, item.type_name)}</span>
                    </div>
                    <div class="text-end">
                        <div>${item.quantity} x ${item.unit_price.toLocaleString()} MP</div>
                        <strong class="text-primary">${item.total_price.toLocaleString()} MP</strong>
                    </div>
                </div>
            </div>
            <div class="card-body">
                ${item.product_type === 'account' && item.assigned_accounts ? `
                    <h6><i class="cil-user"></i> Accounts đã gán:</h6>
                    ${item.assigned_accounts.map(acc => `
                        <div class="account-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>${acc.account_name}</strong>
                                    <br><small class="text-muted">${acc.follower_count.toLocaleString()} followers</small>
                                </div>
                                <span class="badge bg-success">${acc.status}</span>
                            </div>
                        </div>
                    `).join('')}
                ` : ''}

                ${item.product_type === 'videos' && item.assigned_video_links ? `
                    <div class="mt-3">
                        <h6><i class="cil-video"></i> Video Links đã mua:</h6>
                        <div class="table-responsive">
                            <table class="table table-sm table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th><i class="cil-cloud-download me-1"></i>Truy cập</th>
                                        <th><i class="cil-folder me-1"></i>Tên Link</th>
                                        <th><i class="cil-media-play me-1"></i>Số Videos</th>
                                        <th><i class="cil-tag me-1"></i>Loại Video</th>
                                        <th><i class="cil-description me-1"></i>Mô tả</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${item.assigned_video_links.map(link => `
                                        <tr>
                                            <td>
                                                <a href="${link.drive_url}" target="_blank" class="btn btn-sm btn-primary">
                                                    <i class="cil-external-link me-1"></i>Google Drive
                                                </a>
                                            </td>
                                            <td>
                                                <strong class="text-primary">${link.name}</strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-primary">${link.video_count} videos</span>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">${link.video_type || 'Chưa phân loại'}</span>
                                            </td>
                                            <td>
                                                <small class="text-muted">${link.description ? (link.description.length > 50 ? link.description.substring(0, 50) + '...' : link.description) : 'Không có mô tả'}</small>
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                        <div class="mt-2">
                            <small class="text-muted">
                                <i class="cil-info me-1"></i>
                                Tổng cộng: <strong>${item.assigned_video_links.length} video links</strong> với
                                <strong>${item.assigned_video_links.reduce((total, link) => total + link.video_count, 0)} videos</strong>
                            </small>
                        </div>
                    </div>
                ` : ''}

                ${item.product_type !== 'account' && item.product_type !== 'videos' ? `
                    <div class="mt-3">
                        <h6><i class="cil-folder-open"></i> Files sản phẩm:</h6>
                        <div id="productFiles_${item.item_id}">
                            <div class="text-center py-3">
                                <button class="btn btn-outline-primary" onclick="loadProductFiles(${item.product_id}, ${item.item_id})">
                                    <i class="cil-cloud-download"></i> Tải files
                                </button>
                            </div>
                        </div>
                    </div>
                ` : ''}
            </div>
        </div>
    `).join('');
}

function loadProductFiles(productId, itemId) {
    const container = document.getElementById(`productFiles_${itemId}`);

    container.innerHTML = `
        <div class="text-center py-3">
            <i class="cil-reload fa-spin"></i> Đang tải files...
        </div>
    `;

    fetch(`/api/products/${productId}/files`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayProductFiles(data.files, container);
            } else {
                container.innerHTML = `
                    <div class="alert alert-warning">
                        <i class="cil-warning"></i> ${data.error}
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error loading files:', error);
            container.innerHTML = `
                <div class="alert alert-danger">
                    <i class="cil-warning"></i> Lỗi kết nối: ${error.message}
                </div>
            `;
        });
}

function displayProductFiles(files, container) {
    if (files.length === 0) {
        container.innerHTML = `
            <div class="alert alert-info">
                <i class="cil-info"></i> Sản phẩm này chưa có files đính kèm
            </div>
        `;
        return;
    }

    container.innerHTML = `
        <div class="mb-3">
            <span class="badge bg-success">${files.length} files có sẵn</span>
        </div>
        ${files.map(file => `
            <div class="file-item">
                <div class="file-icon ${file.file_type}">
                    <i class="${getFileIcon(file.file_type)}"></i>
                </div>
                <div class="flex-grow-1">
                    <div class="fw-bold">${file.original_name}</div>
                    <small class="text-muted">
                        ${formatFileSize(file.file_size)} • ${file.file_type.toUpperCase()}
                        ${file.is_preview ? ' • <span class="badge bg-info">Preview</span>' : ''}
                    </small>
                    ${file.description ? `<div class="text-muted small mt-1">${file.description}</div>` : ''}
                </div>
                <div class="text-end">
                    <a href="${file.download_url}" class="download-btn" target="_blank">
                        <i class="cil-cloud-download"></i> Tải về
                    </a>
                    <div class="small text-muted mt-1">
                        <i class="cil-chart-line"></i> ${file.download_count} lượt tải
                    </div>
                </div>
            </div>
        `).join('')}
    `;
}

function getFileIcon(type) {
    const icons = {
        'image': 'cil-image',
        'document': 'cil-description',
        'video': 'cil-video',
        'audio': 'cil-audio-spectrum',
        'other': 'cil-file'
    };
    return icons[type] || 'cil-file';
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function formatDate(dateString) {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('vi-VN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

function getStatusClass(status) {
    const classes = {
        'pending': 'bg-warning',
        'completed': 'bg-success',
        'cancelled': 'bg-danger',
        'processing': 'bg-info'
    };
    return classes[status] || 'bg-secondary';
}

function getStatusText(status) {
    const texts = {
        'pending': 'Chờ xử lý',
        'completed': 'Hoàn thành',
        'cancelled': 'Đã hủy',
        'processing': 'Đang xử lý'
    };
    return texts[status] || status;
}

function getProductTypeLabel(type, typeName) {
    // Ưu tiên hiển thị tên ProductType gốc nếu có
    if (typeName) {
        return typeName;
    }

    // Fallback cho các type cũ
    const labels = {
        'account': 'Account Package',
        'win_product': 'Win Product',
        'course': 'Khóa học',
        'aff_package': 'AFF Package'
    };
    return labels[type] || type;
}

function showError(message) {
    document.getElementById('orderInfo').innerHTML = `
        <div class="alert alert-danger">
            <i class="cil-warning"></i> ${message}
        </div>
    `;
    document.getElementById('orderSummary').innerHTML = `
        <div class="alert alert-danger">
            <i class="cil-warning"></i> ${message}
        </div>
    `;
    document.getElementById('orderProducts').innerHTML = `
        <div class="alert alert-danger">
            <i class="cil-warning"></i> ${message}
        </div>
    `;
}



</script>
{% endblock %}
