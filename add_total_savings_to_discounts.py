#!/usr/bin/env python3
"""
Migration script to add total_savings column to Discounts table
"""

import psycopg2
import sys

def get_db_connection():
    """Get PostgreSQL database connection"""
    try:
        conn = psycopg2.connect(
            host="localhost",
            database="sapmmo", 
            user="alandoan",
            password=""
        )
        return conn
    except Exception as e:
        print(f"❌ Database connection error: {e}")
        return None

def main():
    print("🚀 Starting migration: Add total_savings column to Discounts table")
    
    conn = get_db_connection()
    if not conn:
        sys.exit(1)
    
    try:
        cursor = conn.cursor()
        
        # Check if column already exists
        cursor.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'Discounts' AND column_name = 'total_savings'
        """)
        
        if cursor.fetchone():
            print("✅ Column 'total_savings' already exists in Discounts table")
        else:
            # Add total_savings column
            print("🔧 Adding total_savings column to Discounts table...")
            cursor.execute('''
                ALTER TABLE "Discounts" 
                ADD COLUMN total_savings DECIMAL(15,2) DEFAULT 0
            ''')
            
            print("✅ Successfully added total_savings column")
        
        # Update existing records to have total_savings = 0 if NULL
        print("🔧 Updating existing records...")
        cursor.execute('''
            UPDATE "Discounts" 
            SET total_savings = 0 
            WHERE total_savings IS NULL
        ''')
        
        updated_count = cursor.rowcount
        print(f"✅ Updated {updated_count} existing discount records")
        
        conn.commit()
        cursor.close()
        conn.close()
        
        print("🎉 Migration completed successfully!")
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        if conn:
            conn.rollback()
            conn.close()
        sys.exit(1)

if __name__ == "__main__":
    main()
