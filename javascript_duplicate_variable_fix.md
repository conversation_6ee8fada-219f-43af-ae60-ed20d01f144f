# 🔧 Fix JavaScript Error: "Identifier 'teams' has already been declared"

## 🚨 **Lỗi gặp phải:**
```
Uncaught SyntaxError: Identifier 'teams' has already been declared (at accounts:6576:9)
```

## 🔍 **Nguyên nhân:**
- **Nhiều templates** đều khai báo `const teams` và `const users`
- <PERSON><PERSON> navigate giữa các trang, các biến global bị conflict
- JavaScript không cho phép khai báo lại `const` variables trong cùng scope
- **Templates có vấn đề:**
  - `templates/accounts.html`
  - `templates/accounts_coreui.html`
  - `templates/accounts_coreui_new.html`
  - `templates/users_coreui.html`

## ✅ **Giải pháp đã áp dụng:**

### **Trước (gây lỗi):**
```javascript
// Trong mỗi template
const teams = {{ teams|tojson }};
const users = {{ users|tojson }};
```

### **Sau (đã fix):**
```javascript
// Sử dụng global variables để tránh conflict
if (typeof window.teams === 'undefined') {
    window.teams = {{ teams|tojson }};
}
if (typeof window.users === 'undefined') {
    window.users = {{ users|tojson }};
}

// Sử dụng window.teams và window.users trực tiếp trong functions
function getTeamName(teamId) {
    const team = window.teams.find(t => t[0] === teamId);
    return team ? team[1] : '';
}
```

### **Files đã fix:**
- ✅ `templates/accounts.html`
- ✅ `templates/accounts_coreui.html`
- ✅ `templates/accounts_coreui_new.html`
- ✅ `templates/users_coreui.html`

## 🎯 **Cách hoạt động:**

1. **Check existence:** Kiểm tra xem `window.teams` và `window.users` đã tồn tại chưa
2. **Conditional assignment:** Chỉ gán giá trị nếu chưa tồn tại
3. **Local reference:** Tạo local const reference đến global variables
4. **Prevent duplicate:** Tránh khai báo lại khi script load nhiều lần

## 🔧 **Lợi ích:**

- ✅ **Tránh SyntaxError** khi script load 2 lần
- ✅ **Backward compatible** với code hiện tại
- ✅ **Global access** cho các function khác
- ✅ **Performance** không bị ảnh hưởng

## 🧪 **Test:**

1. **Reload trang `/accounts`** nhiều lần
2. **Kiểm tra Console** không còn lỗi "Identifier 'teams' has already been declared"
3. **Verify functionality** các function vẫn hoạt động bình thường
4. **Check variables** `teams` và `users` vẫn accessible

## 📝 **Pattern tương tự:**

Template này cũng đã áp dụng pattern tương tự cho function:
```javascript
if (typeof window.getUserName === 'undefined') {
    window.getUserName = function(userId) {
        // function implementation
    };
}
```

## 🎉 **Kết quả:**

- ✅ **Không còn JavaScript errors**
- ✅ **RefreshAccount function hoạt động bình thường**
- ✅ **UI cập nhật real-time như mong đợi**
- ✅ **Console sạch sẽ, không có lỗi**

Bây giờ trang `/accounts` sẽ load mà không có lỗi JavaScript và tính năng refresh account sẽ hoạt động hoàn hảo! 🚀
