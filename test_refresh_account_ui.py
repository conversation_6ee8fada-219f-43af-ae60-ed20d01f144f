#!/usr/bin/env python3
"""
Test script để kiểm tra refresh account UI update functionality
"""

import requests
import json
import time

def test_refresh_account_api():
    """
    Test API /update_batch_account_stats
    """
    print("🧪 Testing refresh account API...")
    
    # Test data
    test_account_id = 1  # Thay đổi theo account ID thực tế
    
    url = "http://localhost:5000/update_batch_account_stats"
    headers = {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
    }
    data = {
        'account_ids': [test_account_id]
    }
    
    try:
        print(f"📤 Sending request to {url}")
        print(f"📋 Data: {json.dumps(data, indent=2)}")
        
        response = requests.post(url, json=data, headers=headers)
        
        print(f"📥 Response status: {response.status_code}")
        print(f"📋 Response headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Response JSON:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
            # Check response structure
            if 'updated_accounts' in result:
                print(f"✅ Found updated_accounts: {len(result['updated_accounts'])} accounts")
                for account in result['updated_accounts']:
                    print(f"   📊 Account {account.get('account_id')}: {account.get('account_name')}")
                    print(f"      Followers: {account.get('follower_count')}")
                    print(f"      Likes: {account.get('like_count')}")
                    print(f"      Videos: {account.get('total_videos')}")
            else:
                print("❌ No updated_accounts in response")
                
        else:
            print(f"❌ Error response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

def generate_test_javascript():
    """
    Generate JavaScript test code để test trong browser console
    """
    js_code = """
// Test refresh account functionality trong browser console
console.log('🧪 Testing refresh account UI update...');

// Test data
const testAccountId = 1; // Thay đổi theo account ID thực tế

// Mock updated account data
const mockUpdatedAccounts = [{
    account_id: testAccountId,
    account_name: 'test_account',
    follower_count: 12345,
    like_count: 67890,
    total_videos: 42,
    status: 'Đang nuôi'
}];

// Test updateAccountsInTable function
if (typeof updateAccountsInTable === 'function') {
    console.log('✅ updateAccountsInTable function found');
    console.log('📋 Testing with mock data:', mockUpdatedAccounts);
    
    // Call the function
    updateAccountsInTable(mockUpdatedAccounts);
    console.log('✅ Function called successfully');
} else {
    console.log('❌ updateAccountsInTable function not found');
}

// Test actual API call
function testRefreshAPI() {
    console.log('📤 Testing actual API call...');
    
    fetch('/update_batch_account_stats', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({
            account_ids: [testAccountId]
        })
    })
    .then(response => response.json())
    .then(data => {
        console.log('📥 API Response:', data);
        
        if (data.updated_accounts && data.updated_accounts.length > 0) {
            console.log('✅ Got updated_accounts, calling updateAccountsInTable...');
            updateAccountsInTable(data.updated_accounts);
        } else {
            console.log('❌ No updated_accounts in response');
        }
    })
    .catch(error => {
        console.error('❌ API Error:', error);
    });
}

// Run the test
testRefreshAPI();
"""
    
    print("🔧 JavaScript test code:")
    print("=" * 60)
    print(js_code)
    print("=" * 60)
    print("\n📋 Instructions:")
    print("1. Mở trang /accounts trong browser")
    print("2. Mở Developer Console (F12)")
    print("3. Copy và paste đoạn code JavaScript trên")
    print("4. Thay đổi testAccountId thành ID thực tế")
    print("5. Chạy code và xem kết quả")

def main():
    print("🚀 Refresh Account UI Test Suite")
    print("=" * 50)
    
    print("\n1️⃣ Testing API endpoint...")
    # test_refresh_account_api()  # Uncomment để test API
    
    print("\n2️⃣ Generating JavaScript test code...")
    generate_test_javascript()
    
    print("\n✅ Test suite completed!")

if __name__ == "__main__":
    main()
