#!/usr/bin/env python3
"""
Migration script for server: Add total_savings column to Discounts table
Run this on the server after uploading modified files
"""

import sys
import os

# Add current directory to Python path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from db_config import PG_CONFIG
    import psycopg2
    print("✅ Successfully imported db_config")
except ImportError as e:
    print(f"❌ Failed to import db_config: {e}")
    print("📝 Make sure db_config.py exists and contains PG_CONFIG")
    sys.exit(1)

def get_db_connection():
    """Get PostgreSQL database connection using server config"""
    try:
        conn = psycopg2.connect(**PG_CONFIG)
        return conn
    except Exception as e:
        print(f"❌ Database connection error: {e}")
        print(f"📋 Config used: {PG_CONFIG}")
        return None

def check_column_exists(cursor, table_name, column_name):
    """Check if column exists in table"""
    cursor.execute("""
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name = %s AND column_name = %s AND table_schema = 'public'
    """, (table_name, column_name))
    return cursor.fetchone() is not None

def main():
    print("🚀 Server Migration: Add total_savings column to Discounts table")
    print("=" * 60)
    
    # Test database connection
    conn = get_db_connection()
    if not conn:
        print("❌ Cannot connect to database. Please check db_config.py")
        sys.exit(1)
    
    try:
        cursor = conn.cursor()
        
        # Check current database info
        cursor.execute("SELECT current_database(), current_user;")
        db_info = cursor.fetchone()
        print(f"📊 Connected to database: {db_info[0]} as user: {db_info[1]}")
        
        # Check if Discounts table exists
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_name = 'Discounts' AND table_schema = 'public'
        """)
        
        if not cursor.fetchone():
            print("❌ Discounts table not found!")
            print("📝 Please make sure the Discounts table exists before running this migration")
            sys.exit(1)
        
        print("✅ Discounts table found")
        
        # Check current table structure
        print("\n🔍 Checking current Discounts table structure...")
        cursor.execute("""
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'Discounts' AND table_schema = 'public'
            ORDER BY ordinal_position;
        """)
        
        columns = cursor.fetchall()
        print("📋 Current columns:")
        for col in columns:
            nullable = "NULL" if col[2] == "YES" else "NOT NULL"
            default = f" DEFAULT {col[3]}" if col[3] else ""
            print(f"   - {col[0]} ({col[1]}) {nullable}{default}")
        
        # Check if total_savings column already exists
        if check_column_exists(cursor, 'Discounts', 'total_savings'):
            print("\n✅ Column 'total_savings' already exists in Discounts table")
            
            # Update existing records to ensure they have total_savings = 0 if NULL
            print("🔧 Ensuring existing records have total_savings = 0...")
            cursor.execute('''
                UPDATE "Discounts" 
                SET total_savings = 0 
                WHERE total_savings IS NULL
            ''')
            
            updated_count = cursor.rowcount
            print(f"✅ Updated {updated_count} existing discount records")
            
        else:
            # Add total_savings column
            print("\n🔧 Adding total_savings column to Discounts table...")
            cursor.execute('''
                ALTER TABLE "Discounts" 
                ADD COLUMN total_savings DECIMAL(15,2) DEFAULT 0
            ''')
            
            print("✅ Successfully added total_savings column")
            
            # Update existing records to have total_savings = 0
            print("🔧 Initializing existing records with total_savings = 0...")
            cursor.execute('''
                UPDATE "Discounts" 
                SET total_savings = 0 
                WHERE total_savings IS NULL
            ''')
            
            updated_count = cursor.rowcount
            print(f"✅ Initialized {updated_count} existing discount records")
        
        # Verify the column was added successfully
        print("\n🔍 Verifying migration...")
        if check_column_exists(cursor, 'Discounts', 'total_savings'):
            print("✅ total_savings column verified successfully")
            
            # Show sample data
            cursor.execute('''
                SELECT code, used_count, total_savings 
                FROM "Discounts" 
                ORDER BY created_at DESC 
                LIMIT 5
            ''')
            
            sample_data = cursor.fetchall()
            if sample_data:
                print("\n📊 Sample discount data:")
                for row in sample_data:
                    savings = row[2] if row[2] is not None else 0
                    print(f"   - {row[0]}: Used {row[1]} times, Saved {savings:,.0f} MP")
            else:
                print("📊 No discount codes found in database")
        else:
            print("❌ Failed to verify total_savings column")
            sys.exit(1)
        
        # Commit changes
        conn.commit()
        cursor.close()
        conn.close()
        
        print("\n🎉 Migration completed successfully!")
        print("\n📝 Next steps:")
        print("   1. Restart the application service")
        print("   2. Test discount functionality")
        print("   3. Check /admin/marketplace/discounts page")
        print("   4. Test checkout with discount codes")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Migration failed: {e}")
        import traceback
        print(f"📋 Traceback: {traceback.format_exc()}")
        if conn:
            conn.rollback()
            conn.close()
        sys.exit(1)

if __name__ == "__main__":
    main()
