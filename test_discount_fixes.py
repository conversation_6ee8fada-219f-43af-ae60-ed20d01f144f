#!/usr/bin/env python3
"""
Test script to verify discount code fixes
"""

import psycopg2
import json
import requests
from datetime import datetime, timedelta

def get_db_connection():
    """Get PostgreSQL database connection"""
    try:
        conn = psycopg2.connect(
            host="localhost",
            database="sapmmo", 
            user="alandoan",
            password=""
        )
        return conn
    except Exception as e:
        print(f"❌ Database connection error: {e}")
        return None

def test_discount_calculation():
    """Test discount calculation logic"""
    print("🧪 Testing discount calculation logic...")
    
    # Test case 1: 100% discount
    subtotal = 100000  # 100,000 MP
    discount_percentage = 100
    discount_amount = subtotal * (discount_percentage / 100)
    final_amount = subtotal - discount_amount
    
    print(f"📊 Test Case 1 - 100% Discount:")
    print(f"   Subtotal: {subtotal:,} MP")
    print(f"   Discount: {discount_percentage}% = {discount_amount:,} MP")
    print(f"   Final Amount: {final_amount:,} MP")
    print(f"   ✅ Expected: 0 MP, Got: {final_amount:,} MP")
    
    # Test case 2: 50% discount with 50,000 MP balance
    user_balance = 50000
    print(f"\n📊 Test Case 2 - Balance Check:")
    print(f"   User Balance: {user_balance:,} MP")
    print(f"   Final Amount: {final_amount:,} MP")
    print(f"   Can Purchase: {'✅ Yes' if user_balance >= final_amount else '❌ No'}")
    
    return True

def create_test_discount():
    """Create a test discount code"""
    print("\n🔧 Creating test discount code...")
    
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor()
        
        # Create 100% discount code
        test_discount = {
            'code': 'TEST100',
            'description': 'Test discount for fixing bugs - 100% off',
            'discount_type': 'percentage',
            'discount_value': 100.00,
            'min_order_amount': 0,
            'usage_limit': 10,
            'valid_from': datetime.now(),
            'valid_to': datetime.now() + timedelta(days=7)
        }
        
        cursor.execute('''
            INSERT INTO "Discounts" (
                code, description, discount_type, value,
                min_order_value, usage_limit, start_date, end_date, is_active
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            ON CONFLICT (code) DO UPDATE SET
                value = EXCLUDED.value,
                usage_limit = EXCLUDED.usage_limit,
                end_date = EXCLUDED.end_date,
                is_active = true
        ''', (
            test_discount['code'], test_discount['description'],
            test_discount['discount_type'], test_discount['discount_value'],
            test_discount['min_order_amount'], test_discount['usage_limit'],
            test_discount['valid_from'], test_discount['valid_to'], True
        ))
        
        conn.commit()
        cursor.close()
        conn.close()
        
        print(f"✅ Created test discount: {test_discount['code']}")
        return True
        
    except Exception as e:
        print(f"❌ Failed to create test discount: {e}")
        return False

def check_database_structure():
    """Check if database structure is correct"""
    print("\n🔍 Checking database structure...")
    
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor()
        
        # Check Discounts table structure
        cursor.execute('''
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'Discounts' AND table_schema = 'public'
            ORDER BY ordinal_position;
        ''')
        
        columns = cursor.fetchall()
        print("📋 Discounts table columns:")
        for col in columns:
            print(f"   - {col[0]} ({col[1]}) {'NULL' if col[2] == 'YES' else 'NOT NULL'}")
        
        # Check if total_savings column exists
        column_names = [col[0] for col in columns]
        if 'total_savings' in column_names:
            print("✅ total_savings column exists")
        else:
            print("❌ total_savings column missing")
        
        # Check sample discount
        cursor.execute('SELECT COUNT(*) FROM "Discounts" WHERE code = %s', ('TEST100',))
        count = cursor.fetchone()[0]
        print(f"📊 Test discount exists: {'✅ Yes' if count > 0 else '❌ No'}")
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Database check failed: {e}")
        return False

def main():
    print("🚀 Testing Discount Code Fixes")
    print("=" * 50)
    
    # Test 1: Basic calculation logic
    test_discount_calculation()
    
    # Test 2: Database structure
    check_database_structure()
    
    # Test 3: Create test discount
    create_test_discount()
    
    print("\n🎉 Testing completed!")
    print("\n📝 Manual testing steps:")
    print("1. Go to marketplace storefront")
    print("2. Add a product to cart (price > 0)")
    print("3. Apply discount code 'TEST100'")
    print("4. Check if total shows 0 MP correctly")
    print("5. Try to checkout with 0 MP balance")
    print("6. Check transaction history for correct amounts")

if __name__ == "__main__":
    main()
