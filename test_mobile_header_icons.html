<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Mobile Header Icons</title>
    
    <!-- CoreUI CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@coreui/coreui@4.2.6/dist/css/coreui.min.css">
    <!-- CoreUI Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@coreui/icons@2.1.0/css/all.min.css">
    
    <style>
        /* Icon fallback styles */
        .icon-fallback {
            display: inline-block;
            width: 24px;
            height: 24px;
            text-align: center;
            line-height: 24px;
            font-weight: bold;
            border-radius: 4px;
            background-color: #00C6AE;
            color: white;
            font-size: 14px;
        }
        
        .test-section {
            margin: 20px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        
        .icon-test {
            display: inline-block;
            margin: 10px;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Mobile Header Icons</h1>
        
        <div class="test-section">
            <h3>📱 Mobile Header Simulation - New Layout</h3>
            <div class="header" style="background: white; padding: 10px; border: 1px solid #ddd; border-radius: 8px;">
                <!-- Top row: Menu | Logo | Icons -->
                <div class="d-flex align-items-center mb-2">
                    <!-- Left: Menu button -->
                    <button class="btn p-2" style="flex-shrink: 0; width: 44px; height: 44px;">
                        <i class="icon icon-lg cil-menu"></i>
                    </button>

                    <!-- Center: Logo -->
                    <div class="flex-grow-1 d-flex justify-content-center">
                        <div style="background: white; padding: 5px 10px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0, 198, 174, 0.1);">
                            <img src="https://via.placeholder.com/45x45/00C6AE/white?text=LOGO" alt="MIP System" style="height: 45px; width: auto;">
                        </div>
                    </div>

                    <!-- Right: All icons aligned to right -->
                    <div class="d-flex align-items-center" style="flex-shrink: 0;">
                        <a class="nav-link p-2" href="/marketplace" title="Cửa Hàng">
                            <i class="icon icon-lg cil-basket"></i>
                        </a>
                        <a class="nav-link p-2" href="/marketplace/orders" title="Đơn hàng của tôi">
                            <i class="icon icon-lg cil-list-rich"></i>
                        </a>
                        <a class="nav-link p-2" href="#" title="Thông báo">
                            <i class="icon icon-lg cil-bell"></i>
                        </a>
                        <a class="nav-link p-2" href="#" title="User">
                            <i class="icon icon-lg cil-user"></i>
                        </a>
                    </div>
                </div>

                <!-- Bottom row: MP Balance (right aligned) -->
                <div style="width: 100%; display: flex !important; justify-content: flex-end !important; align-items: center !important;">
                    <div style="background: rgba(0, 198, 174, 0.1); border-radius: 8px; padding: 8px 12px; display: inline-block; white-space: nowrap;">
                        <span class="fw-bold" style="color: #00C6AE;">MP: </span>
                        <span class="fw-bold" style="color: #00C6AE;">70.766.460</span>
                        <a href="/deposit" class="btn btn-sm btn-primary ms-2" style="background-color: #00C6AE; border-color: #00C6AE;">
                            <i class="icon cil-credit-card"></i> Nạp MP
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🔧 Individual Icon Tests</h3>
            
            <div class="icon-test">
                <strong>Basket (Cửa Hàng):</strong>
                <i class="icon icon-lg cil-basket"></i>
                <span class="icon-fallback">🛒</span>
            </div>
            
            <div class="icon-test">
                <strong>List Rich (Đơn hàng):</strong>
                <i class="icon icon-lg cil-list-rich"></i>
                <span class="icon-fallback">📋</span>
            </div>
            
            <div class="icon-test">
                <strong>Bell (Thông báo):</strong>
                <i class="icon icon-lg cil-bell"></i>
                <span class="icon-fallback">🔔</span>
            </div>
            
            <div class="icon-test">
                <strong>User:</strong>
                <i class="icon icon-lg cil-user"></i>
                <span class="icon-fallback">👤</span>
            </div>
            
            <div class="icon-test">
                <strong>Menu:</strong>
                <i class="icon icon-lg cil-menu"></i>
                <span class="icon-fallback">☰</span>
            </div>
            
            <div class="icon-test">
                <strong>Credit Card:</strong>
                <i class="icon icon-lg cil-credit-card"></i>
                <span class="icon-fallback">💳</span>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📊 Icon Loading Status</h3>
            <div id="iconStatus"></div>
        </div>
        
        <div class="test-section">
            <h3>🔧 Manual Fallback Test</h3>
            <button onclick="testIconFallbacks()" class="btn btn-primary">Test Icon Fallbacks</button>
            <div id="fallbackResults"></div>
        </div>

        <div class="test-section">
            <h3>🎯 Centering Test</h3>
            <p>Testing different centering methods:</p>

            <div style="border: 1px solid #ddd; margin: 10px 0; padding: 10px;">
                <strong>Method 1: text-align center</strong>
                <div style="text-align: center; background: #f0f0f0; padding: 10px;">
                    <div style="background: rgba(0, 198, 174, 0.1); border-radius: 8px; padding: 8px 12px; display: inline-block;">
                        MP: 70.766.460 <button class="btn btn-sm btn-primary">Nạp MP</button>
                    </div>
                </div>
            </div>

            <div style="border: 1px solid #ddd; margin: 10px 0; padding: 10px;">
                <strong>Method 2: flexbox center</strong>
                <div style="display: flex; justify-content: center; background: #f0f0f0; padding: 10px;">
                    <div style="background: rgba(0, 198, 174, 0.1); border-radius: 8px; padding: 8px 12px;">
                        MP: 70.766.460 <button class="btn btn-sm btn-primary">Nạp MP</button>
                    </div>
                </div>
            </div>

            <div style="border: 1px solid #ddd; margin: 10px 0; padding: 10px;">
                <strong>Method 3: margin auto</strong>
                <div style="background: #f0f0f0; padding: 10px;">
                    <div style="background: rgba(0, 198, 174, 0.1); border-radius: 8px; padding: 8px 12px; margin: 0 auto; width: fit-content;">
                        MP: 70.766.460 <button class="btn btn-sm btn-primary">Nạp MP</button>
                    </div>
                </div>
            </div>

            <div style="border: 1px solid #ddd; margin: 10px 0; padding: 10px;">
                <strong>Method 4: Right Aligned (NEW)</strong>
                <div style="width: 100%; display: flex !important; justify-content: flex-end !important; align-items: center !important; background: #f0f0f0; padding: 10px;">
                    <div style="background: rgba(0, 198, 174, 0.1); border-radius: 8px; padding: 8px 12px; display: inline-block;">
                        MP: 70.766.460 <button class="btn btn-sm btn-primary">Nạp MP</button>
                    </div>
                </div>
            </div>

            <div style="border: 1px solid #ddd; margin: 10px 0; padding: 10px;">
                <strong>Method 5: Text Align Right</strong>
                <div style="text-align: right; background: #f0f0f0; padding: 10px;">
                    <div style="background: rgba(0, 198, 174, 0.1); border-radius: 8px; padding: 8px 12px; display: inline-block;">
                        MP: 70.766.460 <button class="btn btn-sm btn-primary">Nạp MP</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Test icon loading
        function checkIconLoading() {
            const statusDiv = document.getElementById('iconStatus');
            const icons = document.querySelectorAll('i[class*="cil-"]');
            let results = [];
            
            icons.forEach((icon, index) => {
                const computedStyle = window.getComputedStyle(icon, '::before');
                const content = computedStyle.getPropertyValue('content');
                const className = icon.className;
                
                results.push(`
                    <div style="margin: 5px 0; padding: 5px; border: 1px solid #eee;">
                        <strong>Icon ${index + 1}:</strong> ${className}<br>
                        <strong>Content:</strong> ${content || 'none'}<br>
                        <strong>Status:</strong> ${content && content !== 'none' && content !== '""' ? '✅ Loaded' : '❌ Not loaded'}
                    </div>
                `);
            });
            
            statusDiv.innerHTML = results.join('');
        }
        
        // Test fallback function
        function testIconFallbacks() {
            const resultsDiv = document.getElementById('fallbackResults');
            const icons = document.querySelectorAll('i[class*="cil-"]');
            let results = [];
            
            icons.forEach((icon, index) => {
                const originalClass = icon.className;
                
                // Apply fallback
                if (icon.classList.contains('cil-basket')) {
                    icon.className = 'icon-fallback';
                    icon.innerHTML = '🛒';
                    results.push(`Icon ${index + 1}: ${originalClass} → 🛒`);
                } else if (icon.classList.contains('cil-list-rich')) {
                    icon.className = 'icon-fallback';
                    icon.innerHTML = '📋';
                    results.push(`Icon ${index + 1}: ${originalClass} → 📋`);
                } else if (icon.classList.contains('cil-bell')) {
                    icon.className = 'icon-fallback';
                    icon.innerHTML = '🔔';
                    results.push(`Icon ${index + 1}: ${originalClass} → 🔔`);
                } else if (icon.classList.contains('cil-user')) {
                    icon.className = 'icon-fallback';
                    icon.innerHTML = '👤';
                    results.push(`Icon ${index + 1}: ${originalClass} → 👤`);
                } else if (icon.classList.contains('cil-menu')) {
                    icon.className = 'icon-fallback';
                    icon.innerHTML = '☰';
                    results.push(`Icon ${index + 1}: ${originalClass} → ☰`);
                } else if (icon.classList.contains('cil-credit-card')) {
                    icon.className = 'icon-fallback';
                    icon.innerHTML = '💳';
                    results.push(`Icon ${index + 1}: ${originalClass} → 💳`);
                }
            });
            
            resultsDiv.innerHTML = `
                <div style="margin-top: 10px; padding: 10px; background: #f8f9fa; border-radius: 4px;">
                    <strong>Fallback Results:</strong><br>
                    ${results.join('<br>')}
                </div>
            `;
        }
        
        // Run tests when page loads
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(checkIconLoading, 1000);
        });
    </script>
</body>
</html>
